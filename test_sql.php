<?php
/**
 * SQL Syntax Test for Equivalent Product Types Module
 * Run this to test SQL syntax before installation
 *
 * <AUTHOR>
 * @version 1.0.0
 */

// Mock PrestaShop constants for testing
if (!defined('_DB_PREFIX_')) {
    define('_DB_PREFIX_', 'ps_');
}
if (!defined('_MYSQL_ENGINE_')) {
    define('_MYSQL_ENGINE_', 'InnoDB');
}

echo "=== SQL Syntax Test ===\n\n";

// Test SQL statements
$sql = array();

// Table to store equivalent product relationships
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stequivalent_product_groups` (
    `id_group` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `group_hash` varchar(32) NOT NULL,
    `attributes_hash` varchar(255) NOT NULL,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_group`),
    UNIQUE KEY `group_hash` (`group_hash`),
    KEY `attributes_hash` (`attributes_hash`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table to link products to equivalent groups
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stequivalent_product_group_products` (
    `id_group` int(10) unsigned NOT NULL,
    `id_product` int(10) unsigned NOT NULL,
    `id_product_attribute` int(10) unsigned DEFAULT NULL,
    `date_add` datetime NOT NULL,
    PRIMARY KEY (`id_group`, `id_product`),
    KEY `id_product` (`id_product`),
    KEY `id_product_attribute` (`id_product_attribute`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table to store cached sales data for performance
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stequivalent_sales_cache` (
    `id_product` int(10) unsigned NOT NULL,
    `id_product_attribute` int(10) unsigned DEFAULT NULL,
    `period_days` int(3) unsigned NOT NULL DEFAULT 30,
    `total_sales` int(10) unsigned NOT NULL DEFAULT 0,
    `total_quantity` int(10) unsigned NOT NULL DEFAULT 0,
    `last_updated` datetime NOT NULL,
    PRIMARY KEY (`id_product`, `period_days`),
    KEY `id_product_attribute` (`id_product_attribute`),
    KEY `last_updated` (`last_updated`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table to store module configuration per shop
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stequivalent_configuration` (
    `id_configuration` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_shop` int(10) unsigned NOT NULL DEFAULT 1,
    `id_shop_group` int(10) unsigned DEFAULT NULL,
    `name` varchar(32) NOT NULL,
    `value` text,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_configuration`),
    UNIQUE KEY `name_shop` (`name`, `id_shop`),
    KEY `id_shop` (`id_shop`),
    KEY `id_shop_group` (`id_shop_group`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table to store attribute mapping for equivalent detection
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stequivalent_attribute_mapping` (
    `id_mapping` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_attribute_group` int(10) unsigned NOT NULL,
    `attribute_name` varchar(128) NOT NULL,
    `is_enabled` tinyint(1) unsigned NOT NULL DEFAULT 1,
    `weight` int(3) unsigned NOT NULL DEFAULT 1,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_mapping`),
    UNIQUE KEY `id_attribute_group` (`id_attribute_group`),
    KEY `is_enabled` (`is_enabled`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

echo "Testing " . count($sql) . " SQL statements...\n\n";

$errors = 0;
foreach ($sql as $index => $query) {
    echo "Query " . ($index + 1) . ": ";
    
    // Basic syntax validation
    $query_trimmed = trim($query);
    
    // Check for basic SQL syntax issues
    if (empty($query_trimmed)) {
        echo "❌ Empty query\n";
        $errors++;
        continue;
    }
    
    if (!preg_match('/^(CREATE|INSERT|UPDATE|DELETE|SELECT)/i', $query_trimmed)) {
        echo "❌ Invalid SQL command\n";
        $errors++;
        continue;
    }
    
    if (substr($query_trimmed, -1) !== ';') {
        echo "❌ Missing semicolon\n";
        $errors++;
        continue;
    }
    
    // Check for balanced parentheses
    $open_parens = substr_count($query, '(');
    $close_parens = substr_count($query, ')');
    if ($open_parens !== $close_parens) {
        echo "❌ Unbalanced parentheses (open: $open_parens, close: $close_parens)\n";
        $errors++;
        continue;
    }
    
    // Check for balanced backticks
    $backticks = substr_count($query, '`');
    if ($backticks % 2 !== 0) {
        echo "❌ Unbalanced backticks\n";
        $errors++;
        continue;
    }
    
    echo "✅ Syntax OK\n";
}

echo "\n";

// Test attribute mapping queries
echo "Testing attribute mapping queries...\n\n";

$default_attributes = array(
    array('name' => 'Color', 'weight' => 10),
    array('name' => 'Size', 'weight' => 8),
    array('name' => 'Material', 'weight' => 6),
);

foreach ($default_attributes as $index => $attr) {
    echo "Attribute " . ($index + 1) . " (" . $attr['name'] . "): ";
    
    // Test the find query
    $sql_find_attr = 'SELECT ag.id_attribute_group 
                     FROM ' . _DB_PREFIX_ . 'attribute_group ag
                     LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group_lang agl ON ag.id_attribute_group = agl.id_attribute_group
                     WHERE agl.name LIKE "' . $attr['name'] . '"
                     AND agl.id_lang = 1
                     LIMIT 1';
    
    // Basic validation
    if (strpos($sql_find_attr, 'LIMIT 1') === false) {
        echo "❌ Missing LIMIT clause\n";
        $errors++;
        continue;
    }
    
    // Test the insert query
    $sql_insert = 'INSERT INTO ' . _DB_PREFIX_ . 'stequivalent_attribute_mapping 
                  (id_attribute_group, attribute_name, is_enabled, weight, date_add, date_upd) 
                  VALUES (1, "' . $attr['name'] . '", 1, ' . $attr['weight'] . ', NOW(), NOW())';
    
    if (strpos($sql_insert, 'VALUES') === false) {
        echo "❌ Invalid INSERT syntax\n";
        $errors++;
        continue;
    }
    
    echo "✅ Queries OK\n";
}

echo "\n=== Test Results ===\n";
if ($errors === 0) {
    echo "✅ All SQL statements passed syntax validation!\n";
    echo "The module should install without SQL errors.\n";
} else {
    echo "❌ Found $errors SQL syntax issues.\n";
    echo "Please review and fix the SQL statements before installation.\n";
}

echo "\n=== Generated SQL Preview ===\n";
foreach ($sql as $index => $query) {
    echo "\n-- Query " . ($index + 1) . ":\n";
    echo $query . "\n";
}
?>
