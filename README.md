# Equivalent Product Types Module for PrestaShop 9.0

**Author:** Sathi  
**Version:** 1.0.0  
**License:** MIT  
**Compatibility:** PrestaShop 9.0+

## 📋 Overview

The Equivalent Product Types module automatically displays all product variants with similar specifications (color, size, reference, etc.) directly on the product page. It provides customers with instant access to related products and gives merchants valuable insights into product performance through comprehensive analytics.

## ✨ Features

### 🔎 Front Office Features
- **Automatic Detection**: Finds products with matching specifications based on configurable attributes
- **Multiple Display Modes**: Grid, List, and Table layouts
- **Comprehensive Product Info**: Images, prices, stock status, and sales data
- **Responsive Design**: Mobile-friendly interface that works on all devices
- **Real-time Stock Updates**: Dynamic stock status display
- **Sales Analytics**: Shows units sold over configurable time periods

### ⚙️ Back Office Features
- **Flexible Configuration**: Enable/disable module per product page
- **Attribute Mapping**: Configure which attributes determine equivalency
- **Display Customization**: Control what information is shown
- **Sales Period Configuration**: Set custom time ranges for sales data
- **Advanced Analytics**: Comprehensive reporting and statistics
- **Multi-language Support**: Full translation support
- **Multi-store Compatibility**: Works with multiple stores

## 🚀 Installation

### Prerequisites
- PrestaShop 9.0 or higher
- PHP 8.1 or higher
- MySQL 5.7 or higher

### Installation Steps

1. **Download the Module**
   ```bash
   # Clone or download the module files
   git clone https://github.com/your-repo/stequivalentproducttype.git
   ```

2. **Run Setup (Recommended)**
   ```bash
   cd stequivalentproducttype
   php setup.php
   ```
   This will check dependencies and set up autoloading automatically.

3. **Upload to PrestaShop**
   - Copy the module folder to `/modules/stequivalentproducttype/`
   - Or upload via PrestaShop Admin → Modules → Upload a module

4. **Install via Admin Panel**
   - Go to Modules → Module Manager
   - Find "Equivalent Product Types"
   - Click "Install"

5. **Configure the Module**
   - After installation, click "Configure"
   - Set your preferred options
   - Save configuration

**Note:** The module works with or without Composer. If Composer is not available, it will automatically use built-in manual autoloading.

## 🔧 Configuration

### Basic Settings

1. **Enable Module**: Turn the module on/off globally
2. **Attributes to Match**: Select which product attributes to use for matching:
   - ✅ Color
   - ✅ Size  
   - ✅ Reference
   - ✅ Material
   - ✅ Custom attributes

3. **Display Options**:
   - **Display Mode**: Grid, List, or Table
   - **Show Images**: Display product thumbnails
   - **Show Prices**: Display product prices
   - **Show Stock**: Display availability status
   - **Show Sales**: Display sales statistics

4. **Sales Configuration**:
   - **Sales Period**: Number of days for sales calculations (default: 30)
   - **Hide Out of Stock**: Option to hide unavailable products

### Advanced Configuration

Access advanced settings via **Modules → Equivalent Product Types → Advanced Settings**:

- **Attribute Mapping**: Map PrestaShop attributes to module functionality
- **Weight Configuration**: Set priority for different attributes
- **Performance Settings**: Configure caching and optimization
- **Analytics Dashboard**: View detailed statistics and reports

## 📊 Analytics & Reporting

The module provides comprehensive analytics:

### Key Metrics
- **Total Equivalent Groups**: Number of product groups created
- **Products with Equivalents**: Count of products that have similar items
- **Cache Performance**: System performance metrics
- **Sales Analytics**: Revenue and quantity data

### Reports Available
- **Top Performing Groups**: Best-selling equivalent product groups
- **Attribute Usage**: Statistics on which attributes are most effective
- **Stock Alerts**: Low stock warnings for equivalent products
- **Performance Trends**: Sales trends and forecasting

## 🎨 Customization

### Template Customization

Templates are located in `views/templates/hook/`:
- `equivalent_products.tpl` - Main display template
- Supports all three display modes (Grid, List, Table)

### CSS Customization

Styles are in `views/css/stequivalent.css`:
- Fully responsive design
- Bootstrap-compatible
- Easy to customize colors and layouts

### JavaScript Functionality

Interactive features in `views/js/stequivalent.js`:
- AJAX loading
- Filtering capabilities
- Analytics tracking
- Responsive behavior

## 🔌 Hooks Used

The module registers the following hooks:
- `displayProductExtraContent` - Main product page display
- `displayProductAdditionalInfo` - Additional product information
- `displayRightColumnProduct` - Right sidebar
- `displayLeftColumnProduct` - Left sidebar
- `displayFooterProduct` - Product page footer
- `header` - CSS/JS inclusion
- `actionProductUpdate` - Cache management
- `actionProductAdd` - Cache management
- `actionProductDelete` - Cache management

## 🗄️ Database Structure

The module creates the following tables:

### Core Tables
- `stequivalent_product_groups` - Product group definitions
- `stequivalent_product_group_products` - Product-to-group relationships
- `stequivalent_sales_cache` - Cached sales data for performance
- `stequivalent_configuration` - Module configuration per shop
- `stequivalent_attribute_mapping` - Attribute mapping configuration

## 🌐 Multi-language Support

Translation files are located in `translations/`:
- `en.php` - English (default)
- Add additional language files as needed

To add a new language:
1. Copy `en.php` to `[language_code].php`
2. Translate all text strings
3. Clear cache in PrestaShop admin

## 🔧 Troubleshooting

### Common Issues

**Module not displaying products:**
- Check if attributes are properly configured
- Verify products have the required attributes
- Ensure module is enabled in configuration

**Performance issues:**
- Clear the sales cache via admin panel
- Reduce the number of matched attributes
- Optimize attribute weights

**Display problems:**
- Clear PrestaShop cache
- Check template compatibility
- Verify CSS/JS files are loading

### Debug Mode

Enable debug mode by adding to your `config/defines.inc.php`:
```php
define('_PS_MODE_DEV_', true);
```

### Cache Management

Clear module cache:
1. Go to Advanced Parameters → Performance
2. Clear cache
3. Or use the module's built-in cache clearing function

## 🚀 Performance Optimization

### Best Practices
- Use attribute weights to prioritize important matching criteria
- Disable unused attributes to improve performance
- Configure appropriate sales cache periods
- Regular cache maintenance

### Caching Strategy
- Sales data is cached for 1 hour by default
- Product groups are cached until products are updated
- Image URLs are optimized for performance

## 🔒 Security

The module follows PrestaShop security best practices:
- SQL injection prevention
- XSS protection
- CSRF token validation
- Input sanitization
- Secure AJAX endpoints

## 📈 Roadmap

Future enhancements planned:
- AI-powered product matching
- Advanced analytics dashboard
- Integration with recommendation engines
- A/B testing capabilities
- Enhanced mobile experience

## 🤝 Support

For support and questions:
- Check the documentation first
- Review common issues in troubleshooting
- Contact: <EMAIL>

## 📄 License

This module is released under the MIT License. See LICENSE file for details.

## 🙏 Credits

Developed by Sathi for PrestaShop 9.0
Special thanks to the PrestaShop community for their valuable feedback and contributions.

---

**Version History:**
- v1.0.0 - Initial release with full feature set
