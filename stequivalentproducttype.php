<?php
/**
 * Equivalent Product Types Module for PrestaShop 9.0
 *
 * <AUTHOR>
 * @version 1.0.0
 * @license MIT
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

// Autoload classes - handle both Composer and manual loading
if (file_exists(dirname(__FILE__) . '/vendor/autoload.php')) {
    require_once dirname(__FILE__) . '/vendor/autoload.php';
} else {
    // Manual autoloader for when Composer is not available
    spl_autoload_register(function ($class) {
        // Only handle our namespace
        if (strpos($class, 'StEquivalentProductType\\') === 0) {
            $classPath = str_replace('StEquivalentProductType\\', '', $class);
            $classPath = str_replace('\\', DIRECTORY_SEPARATOR, $classPath);
            $file = dirname(__FILE__) . '/src/' . $classPath . '.php';

            if (file_exists($file)) {
                require_once $file;
            }
        }
    });
}

class StEquivalentProductType extends Module
{
    public function __construct()
    {
        $this->name = 'stequivalentproducttype';
        $this->tab = 'front_office_features';
        $this->version = '1.0.0';
        $this->author = 'Sathi';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => '1.7',
            'max' => _PS_VERSION_
        ];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Equivalent Product Types');
        $this->description = $this->l('Display all product variants with similar specifications (color, size, reference, etc.) to the currently viewed product directly on the product page.');
        $this->confirmUninstall = $this->l('Are you sure you want to uninstall this module?');

        if (!Configuration::get('STEQUIVALENT_ENABLED')) {
            $this->warning = $this->l('No name provided');
        }
    }

    /**
     * Install the module
     */
    public function install()
    {
        include(dirname(__FILE__) . '/sql/install.php');

        return parent::install() &&
            $this->registerHook('displayProductExtraContent') &&
            $this->registerHook('displayProductAdditionalInfo') &&
            $this->registerHook('displayRightColumnProduct') &&
            $this->registerHook('displayLeftColumnProduct') &&
            $this->registerHook('displayFooterProduct') &&
            $this->registerHook('actionProductUpdate') &&
            $this->registerHook('actionProductAdd') &&
            $this->registerHook('actionProductDelete') &&
            $this->registerHook('header') &&
            Configuration::updateValue('STEQUIVALENT_ENABLED', true) &&
            Configuration::updateValue('STEQUIVALENT_ATTRIBUTES', json_encode(['color', 'size'])) &&
            Configuration::updateValue('STEQUIVALENT_DISPLAY_MODE', 'grid') &&
            Configuration::updateValue('STEQUIVALENT_SHOW_IMAGES', true) &&
            Configuration::updateValue('STEQUIVALENT_SHOW_PRICE', true) &&
            Configuration::updateValue('STEQUIVALENT_SHOW_STOCK', true) &&
            Configuration::updateValue('STEQUIVALENT_SHOW_SALES', true) &&
            Configuration::updateValue('STEQUIVALENT_SALES_PERIOD', 30) &&
            Configuration::updateValue('STEQUIVALENT_HIDE_OUT_OF_STOCK', false);
    }

    /**
     * Uninstall the module
     */
    public function uninstall()
    {
        include(dirname(__FILE__) . '/sql/uninstall.php');

        return Configuration::deleteByName('STEQUIVALENT_ENABLED') &&
            Configuration::deleteByName('STEQUIVALENT_ATTRIBUTES') &&
            Configuration::deleteByName('STEQUIVALENT_DISPLAY_MODE') &&
            Configuration::deleteByName('STEQUIVALENT_SHOW_IMAGES') &&
            Configuration::deleteByName('STEQUIVALENT_SHOW_PRICE') &&
            Configuration::deleteByName('STEQUIVALENT_SHOW_STOCK') &&
            Configuration::deleteByName('STEQUIVALENT_SHOW_SALES') &&
            Configuration::deleteByName('STEQUIVALENT_SALES_PERIOD') &&
            Configuration::deleteByName('STEQUIVALENT_HIDE_OUT_OF_STOCK') &&
            parent::uninstall();
    }

    /**
     * Load the configuration form
     */
    public function getContent()
    {
        $output = null;

        if (Tools::isSubmit('submit' . $this->name)) {
            $stequivalent_enabled = strval(Tools::getValue('STEQUIVALENT_ENABLED'));
            $stequivalent_attributes = Tools::getValue('STEQUIVALENT_ATTRIBUTES');
            $stequivalent_display_mode = strval(Tools::getValue('STEQUIVALENT_DISPLAY_MODE'));
            $stequivalent_show_images = (bool)Tools::getValue('STEQUIVALENT_SHOW_IMAGES');
            $stequivalent_show_price = (bool)Tools::getValue('STEQUIVALENT_SHOW_PRICE');
            $stequivalent_show_stock = (bool)Tools::getValue('STEQUIVALENT_SHOW_STOCK');
            $stequivalent_show_sales = (bool)Tools::getValue('STEQUIVALENT_SHOW_SALES');
            $stequivalent_sales_period = (int)Tools::getValue('STEQUIVALENT_SALES_PERIOD');
            $stequivalent_hide_out_of_stock = (bool)Tools::getValue('STEQUIVALENT_HIDE_OUT_OF_STOCK');

            if (!$stequivalent_enabled || empty($stequivalent_enabled) || !Validate::isGenericName($stequivalent_enabled)) {
                $output .= $this->displayError($this->l('Invalid Configuration value'));
            } else {
                Configuration::updateValue('STEQUIVALENT_ENABLED', $stequivalent_enabled);
                Configuration::updateValue('STEQUIVALENT_ATTRIBUTES', json_encode($stequivalent_attributes));
                Configuration::updateValue('STEQUIVALENT_DISPLAY_MODE', $stequivalent_display_mode);
                Configuration::updateValue('STEQUIVALENT_SHOW_IMAGES', $stequivalent_show_images);
                Configuration::updateValue('STEQUIVALENT_SHOW_PRICE', $stequivalent_show_price);
                Configuration::updateValue('STEQUIVALENT_SHOW_STOCK', $stequivalent_show_stock);
                Configuration::updateValue('STEQUIVALENT_SHOW_SALES', $stequivalent_show_sales);
                Configuration::updateValue('STEQUIVALENT_SALES_PERIOD', $stequivalent_sales_period);
                Configuration::updateValue('STEQUIVALENT_HIDE_OUT_OF_STOCK', $stequivalent_hide_out_of_stock);
                $output .= $this->displayConfirmation($this->l('Settings updated'));
            }
        }

        return $output . $this->displayForm();
    }

    /**
     * Create the form that will be displayed in the configuration of your module.
     */
    protected function displayForm()
    {
        // Get default language
        $default_lang = (int)Configuration::get('PS_LANG_DEFAULT');

        // Init Fields form array
        $fields_form[0]['form'] = [
            'legend' => [
                'title' => $this->l('Settings'),
            ],
            'input' => [
                [
                    'type' => 'switch',
                    'label' => $this->l('Enable Module'),
                    'name' => 'STEQUIVALENT_ENABLED',
                    'is_bool' => true,
                    'desc' => $this->l('Enable or disable the equivalent product types module'),
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => true,
                            'label' => $this->l('Enabled')
                        ],
                        [
                            'id' => 'active_off',
                            'value' => false,
                            'label' => $this->l('Disabled')
                        ]
                    ],
                ],
                [
                    'type' => 'checkbox',
                    'label' => $this->l('Attributes to Match'),
                    'name' => 'STEQUIVALENT_ATTRIBUTES',
                    'desc' => $this->l('Select which attributes should be used to determine equivalent specifications'),
                    'values' => [
                        'query' => [
                            [
                                'id' => 'color',
                                'name' => $this->l('Color'),
                                'val' => '1'
                            ],
                            [
                                'id' => 'size',
                                'name' => $this->l('Size'),
                                'val' => '1'
                            ],
                            [
                                'id' => 'reference',
                                'name' => $this->l('Reference'),
                                'val' => '1'
                            ],
                            [
                                'id' => 'material',
                                'name' => $this->l('Material'),
                                'val' => '1'
                            ]
                        ],
                        'id' => 'id',
                        'name' => 'name'
                    ]
                ],
                [
                    'type' => 'select',
                    'label' => $this->l('Display Mode'),
                    'name' => 'STEQUIVALENT_DISPLAY_MODE',
                    'desc' => $this->l('Choose how to display equivalent products'),
                    'options' => [
                        'query' => [
                            [
                                'id_option' => 'grid',
                                'name' => $this->l('Grid')
                            ],
                            [
                                'id_option' => 'list',
                                'name' => $this->l('List')
                            ],
                            [
                                'id_option' => 'table',
                                'name' => $this->l('Table')
                            ]
                        ],
                        'id' => 'id_option',
                        'name' => 'name'
                    ]
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Show Product Images'),
                    'name' => 'STEQUIVALENT_SHOW_IMAGES',
                    'is_bool' => true,
                    'desc' => $this->l('Display product images for equivalent products'),
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => true,
                            'label' => $this->l('Yes')
                        ],
                        [
                            'id' => 'active_off',
                            'value' => false,
                            'label' => $this->l('No')
                        ]
                    ],
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Show Prices'),
                    'name' => 'STEQUIVALENT_SHOW_PRICE',
                    'is_bool' => true,
                    'desc' => $this->l('Display prices for equivalent products'),
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => true,
                            'label' => $this->l('Yes')
                        ],
                        [
                            'id' => 'active_off',
                            'value' => false,
                            'label' => $this->l('No')
                        ]
                    ],
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Show Stock Information'),
                    'name' => 'STEQUIVALENT_SHOW_STOCK',
                    'is_bool' => true,
                    'desc' => $this->l('Display stock availability for equivalent products'),
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => true,
                            'label' => $this->l('Yes')
                        ],
                        [
                            'id' => 'active_off',
                            'value' => false,
                            'label' => $this->l('No')
                        ]
                    ],
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Show Sales Information'),
                    'name' => 'STEQUIVALENT_SHOW_SALES',
                    'is_bool' => true,
                    'desc' => $this->l('Display sales data for equivalent products'),
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => true,
                            'label' => $this->l('Yes')
                        ],
                        [
                            'id' => 'active_off',
                            'value' => false,
                            'label' => $this->l('No')
                        ]
                    ],
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Sales Period (days)'),
                    'name' => 'STEQUIVALENT_SALES_PERIOD',
                    'size' => 20,
                    'required' => true,
                    'desc' => $this->l('Number of days to calculate sales data (e.g., 30 for last 30 days)')
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Hide Out of Stock Products'),
                    'name' => 'STEQUIVALENT_HIDE_OUT_OF_STOCK',
                    'is_bool' => true,
                    'desc' => $this->l('Hide equivalent products that are out of stock'),
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => true,
                            'label' => $this->l('Yes')
                        ],
                        [
                            'id' => 'active_off',
                            'value' => false,
                            'label' => $this->l('No')
                        ]
                    ],
                ]
            ],
            'submit' => [
                'title' => $this->l('Save'),
                'class' => 'btn btn-default pull-right'
            ]
        ];

        $helper = new HelperForm();

        // Module, token and currentIndex
        $helper->module = $this;
        $helper->name_controller = $this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');
        $helper->currentIndex = AdminController::$currentIndex . '&configure=' . $this->name;

        // Language
        $helper->default_form_language = $default_lang;
        $helper->allow_employee_form_lang = $default_lang;

        // Title and toolbar
        $helper->title = $this->displayName;
        $helper->show_toolbar = true;        // false -> remove toolbar
        $helper->toolbar_scroll = true;      // yes - > Toolbar is always visible on the top of the screen.
        $helper->submit_action = 'submit' . $this->name;
        $helper->toolbar_btn = [
            'save' => [
                'desc' => $this->l('Save'),
                'href' => AdminController::$currentIndex . '&configure=' . $this->name . '&save' . $this->name .
                '&token=' . Tools::getAdminTokenLite('AdminModules'),
            ],
            'back' => [
                'href' => AdminController::$currentIndex . '&token=' . Tools::getAdminTokenLite('AdminModules'),
                'desc' => $this->l('Back to list')
            ]
        ];

        // Load current value
        $helper->fields_value['STEQUIVALENT_ENABLED'] = Configuration::get('STEQUIVALENT_ENABLED');
        $attributes = json_decode(Configuration::get('STEQUIVALENT_ATTRIBUTES'), true);
        if ($attributes) {
            foreach ($attributes as $attr) {
                $helper->fields_value['STEQUIVALENT_ATTRIBUTES_' . $attr] = true;
            }
        }
        $helper->fields_value['STEQUIVALENT_DISPLAY_MODE'] = Configuration::get('STEQUIVALENT_DISPLAY_MODE');
        $helper->fields_value['STEQUIVALENT_SHOW_IMAGES'] = Configuration::get('STEQUIVALENT_SHOW_IMAGES');
        $helper->fields_value['STEQUIVALENT_SHOW_PRICE'] = Configuration::get('STEQUIVALENT_SHOW_PRICE');
        $helper->fields_value['STEQUIVALENT_SHOW_STOCK'] = Configuration::get('STEQUIVALENT_SHOW_STOCK');
        $helper->fields_value['STEQUIVALENT_SHOW_SALES'] = Configuration::get('STEQUIVALENT_SHOW_SALES');
        $helper->fields_value['STEQUIVALENT_SALES_PERIOD'] = Configuration::get('STEQUIVALENT_SALES_PERIOD');
        $helper->fields_value['STEQUIVALENT_HIDE_OUT_OF_STOCK'] = Configuration::get('STEQUIVALENT_HIDE_OUT_OF_STOCK');

        return $helper->generateForm($fields_form);
    }

    /**
     * Hook: Display equivalent products on product page
     */
    public function hookDisplayProductExtraContent($params)
    {
        if (!Configuration::get('STEQUIVALENT_ENABLED')) {
            return '';
        }

        $id_product = (int)Tools::getValue('id_product');
        $id_product_attribute = (int)Tools::getValue('id_product_attribute');

        if (!$id_product) {
            return '';
        }

        require_once dirname(__FILE__) . '/src/Service/EquivalentProductService.php';
        $service = new \StEquivalentProductType\Service\EquivalentProductService();

        $equivalent_products = $service->findEquivalentProducts(
            $id_product,
            $id_product_attribute,
            $this->context->language->id,
            $this->context->shop->id
        );

        if (empty($equivalent_products)) {
            return '';
        }

        $this->context->smarty->assign([
            'equivalent_products' => $equivalent_products,
            'display_mode' => Configuration::get('STEQUIVALENT_DISPLAY_MODE'),
            'show_images' => Configuration::get('STEQUIVALENT_SHOW_IMAGES'),
            'show_price' => Configuration::get('STEQUIVALENT_SHOW_PRICE'),
            'show_stock' => Configuration::get('STEQUIVALENT_SHOW_STOCK'),
            'show_sales' => Configuration::get('STEQUIVALENT_SHOW_SALES'),
            'module_dir' => $this->_path,
        ]);

        return $this->display(__FILE__, 'views/templates/hook/equivalent_products.tpl');
    }

    /**
     * Hook: Display equivalent products in additional info section
     */
    public function hookDisplayProductAdditionalInfo($params)
    {
        return $this->hookDisplayProductExtraContent($params);
    }

    /**
     * Hook: Display equivalent products in right column
     */
    public function hookDisplayRightColumnProduct($params)
    {
        if (!Configuration::get('STEQUIVALENT_ENABLED')) {
            return '';
        }

        return $this->hookDisplayProductExtraContent($params);
    }

    /**
     * Hook: Display equivalent products in left column
     */
    public function hookDisplayLeftColumnProduct($params)
    {
        if (!Configuration::get('STEQUIVALENT_ENABLED')) {
            return '';
        }

        return $this->hookDisplayProductExtraContent($params);
    }

    /**
     * Hook: Display equivalent products in footer
     */
    public function hookDisplayFooterProduct($params)
    {
        if (!Configuration::get('STEQUIVALENT_ENABLED')) {
            return '';
        }

        return $this->hookDisplayProductExtraContent($params);
    }

    /**
     * Hook: Add CSS and JS to header
     */
    public function hookHeader($params)
    {
        if (!Configuration::get('STEQUIVALENT_ENABLED')) {
            return;
        }

        // Only add assets on product pages
        if ($this->context->controller->php_self == 'product') {
            $this->context->controller->addCSS($this->_path . 'views/css/stequivalent.css');
            $this->context->controller->addJS($this->_path . 'views/js/stequivalent.js');
        }
    }

    /**
     * Hook: Clear cache when product is updated
     */
    public function hookActionProductUpdate($params)
    {
        $this->clearProductCache($params['id_product']);
    }

    /**
     * Hook: Clear cache when product is added
     */
    public function hookActionProductAdd($params)
    {
        $this->clearProductCache($params['id_product']);
    }

    /**
     * Hook: Clear cache when product is deleted
     */
    public function hookActionProductDelete($params)
    {
        $this->clearProductCache($params['id_product']);
    }

    /**
     * Clear product cache
     */
    protected function clearProductCache($id_product)
    {
        Db::getInstance()->delete('stequivalent_sales_cache', 'id_product = ' . (int)$id_product);

        // Also clear cache for related products
        Db::getInstance()->execute('
            DELETE FROM ' . _DB_PREFIX_ . 'stequivalent_sales_cache
            WHERE id_product IN (
                SELECT DISTINCT pgp.id_product
                FROM ' . _DB_PREFIX_ . 'stequivalent_product_group_products pgp
                WHERE pgp.id_group IN (
                    SELECT id_group
                    FROM ' . _DB_PREFIX_ . 'stequivalent_product_group_products
                    WHERE id_product = ' . (int)$id_product . '
                )
            )
        ');
    }
}
