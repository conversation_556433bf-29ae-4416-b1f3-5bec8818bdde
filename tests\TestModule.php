<?php
/**
 * Basic Testing for Equivalent Product Types Module
 *
 * <AUTHOR>
 * @version 1.0.0
 */

require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';
require_once dirname(__FILE__) . '/../stequivalentproducttype.php';

class TestModule
{
    private $module;
    private $errors = array();
    private $successes = array();

    public function __construct()
    {
        $this->module = new StEquivalentProductType();
    }

    /**
     * Run all tests
     */
    public function runAllTests()
    {
        echo "<h1>Equivalent Product Types Module - Test Suite</h1>\n";
        echo "<style>
            .success { color: green; font-weight: bold; }
            .error { color: red; font-weight: bold; }
            .test-section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
            .test-result { margin: 5px 0; padding: 5px; }
        </style>\n";

        $this->testModuleInstallation();
        $this->testDatabaseTables();
        $this->testConfiguration();
        $this->testServices();
        $this->testHooks();
        $this->testFrontOffice();
        
        $this->displayResults();
    }

    /**
     * Test module installation
     */
    private function testModuleInstallation()
    {
        echo "<div class='test-section'><h2>Module Installation Tests</h2>\n";

        // Test module object creation
        if ($this->module instanceof StEquivalentProductType) {
            $this->addSuccess("Module object created successfully");
        } else {
            $this->addError("Failed to create module object");
        }

        // Test module properties
        if ($this->module->name === 'stequivalentproducttype') {
            $this->addSuccess("Module name is correct");
        } else {
            $this->addError("Module name is incorrect: " . $this->module->name);
        }

        if ($this->module->author === 'Sathi') {
            $this->addSuccess("Module author is correct");
        } else {
            $this->addError("Module author is incorrect: " . $this->module->author);
        }

        // Test module installation status
        if ($this->module->id > 0) {
            $this->addSuccess("Module is installed (ID: " . $this->module->id . ")");
        } else {
            $this->addError("Module is not installed");
        }

        echo "</div>\n";
    }

    /**
     * Test database tables
     */
    private function testDatabaseTables()
    {
        echo "<div class='test-section'><h2>Database Tables Tests</h2>\n";

        $required_tables = array(
            'stequivalent_product_groups',
            'stequivalent_product_group_products',
            'stequivalent_sales_cache',
            'stequivalent_configuration',
            'stequivalent_attribute_mapping'
        );

        foreach ($required_tables as $table) {
            $full_table_name = _DB_PREFIX_ . $table;
            $exists = Db::getInstance()->executeS("SHOW TABLES LIKE '$full_table_name'");
            
            if (!empty($exists)) {
                $this->addSuccess("Table $table exists");
                
                // Test table structure
                $columns = Db::getInstance()->executeS("DESCRIBE $full_table_name");
                if (!empty($columns)) {
                    $this->addSuccess("Table $table has proper structure (" . count($columns) . " columns)");
                } else {
                    $this->addError("Table $table has no columns");
                }
            } else {
                $this->addError("Table $table does not exist");
            }
        }

        // Test default data
        $default_mappings = Db::getInstance()->getValue("
            SELECT COUNT(*) FROM " . _DB_PREFIX_ . "stequivalent_attribute_mapping
        ");
        
        if ($default_mappings > 0) {
            $this->addSuccess("Default attribute mappings exist ($default_mappings mappings)");
        } else {
            $this->addError("No default attribute mappings found");
        }

        echo "</div>\n";
    }

    /**
     * Test configuration
     */
    private function testConfiguration()
    {
        echo "<div class='test-section'><h2>Configuration Tests</h2>\n";

        $config_keys = array(
            'STEQUIVALENT_ENABLED',
            'STEQUIVALENT_DISPLAY_MODE',
            'STEQUIVALENT_SHOW_IMAGES',
            'STEQUIVALENT_SHOW_PRICE',
            'STEQUIVALENT_SHOW_STOCK',
            'STEQUIVALENT_SHOW_SALES'
        );

        foreach ($config_keys as $key) {
            $value = Configuration::get($key);
            if ($value !== false) {
                $this->addSuccess("Configuration $key exists (value: $value)");
            } else {
                $this->addError("Configuration $key does not exist");
            }
        }

        // Test configuration form
        try {
            $form = $this->module->getConfigForm();
            if (!empty($form)) {
                $this->addSuccess("Configuration form generated successfully");
            } else {
                $this->addError("Configuration form is empty");
            }
        } catch (Exception $e) {
            $this->addError("Error generating configuration form: " . $e->getMessage());
        }

        echo "</div>\n";
    }

    /**
     * Test services
     */
    private function testServices()
    {
        echo "<div class='test-section'><h2>Services Tests</h2>\n";

        // Test EquivalentProductService
        try {
            require_once dirname(__FILE__) . '/../src/Service/EquivalentProductService.php';
            $service = new \StEquivalentProductType\Service\EquivalentProductService();
            $this->addSuccess("EquivalentProductService instantiated successfully");

            // Test with a sample product (if exists)
            $sample_product = Db::getInstance()->getRow("
                SELECT id_product FROM " . _DB_PREFIX_ . "product 
                WHERE active = 1 LIMIT 1
            ");

            if ($sample_product) {
                $equivalent_products = $service->findEquivalentProducts(
                    $sample_product['id_product'],
                    null,
                    Context::getContext()->language->id,
                    Context::getContext()->shop->id
                );
                
                $this->addSuccess("findEquivalentProducts method executed (found " . count($equivalent_products) . " products)");
            } else {
                $this->addError("No active products found for testing");
            }

        } catch (Exception $e) {
            $this->addError("Error testing EquivalentProductService: " . $e->getMessage());
        }

        // Test ProductGroupService
        try {
            require_once dirname(__FILE__) . '/../src/Service/ProductGroupService.php';
            $groupService = new \StEquivalentProductType\Service\ProductGroupService();
            $this->addSuccess("ProductGroupService instantiated successfully");
        } catch (Exception $e) {
            $this->addError("Error testing ProductGroupService: " . $e->getMessage());
        }

        // Test AnalyticsService
        try {
            require_once dirname(__FILE__) . '/../src/Service/AnalyticsService.php';
            $analyticsService = new \StEquivalentProductType\Service\AnalyticsService();
            $this->addSuccess("AnalyticsService instantiated successfully");
        } catch (Exception $e) {
            $this->addError("Error testing AnalyticsService: " . $e->getMessage());
        }

        echo "</div>\n";
    }

    /**
     * Test hooks
     */
    private function testHooks()
    {
        echo "<div class='test-section'><h2>Hooks Tests</h2>\n";

        $required_hooks = array(
            'displayProductExtraContent',
            'displayProductAdditionalInfo',
            'displayRightColumnProduct',
            'displayLeftColumnProduct',
            'displayFooterProduct',
            'header',
            'actionProductUpdate',
            'actionProductAdd',
            'actionProductDelete'
        );

        foreach ($required_hooks as $hook_name) {
            $hook_registered = Db::getInstance()->getValue("
                SELECT COUNT(*) FROM " . _DB_PREFIX_ . "hook_module hm
                LEFT JOIN " . _DB_PREFIX_ . "hook h ON h.id_hook = hm.id_hook
                WHERE h.name = '" . pSQL($hook_name) . "' 
                AND hm.id_module = " . (int)$this->module->id
            );

            if ($hook_registered > 0) {
                $this->addSuccess("Hook $hook_name is registered");
            } else {
                $this->addError("Hook $hook_name is not registered");
            }
        }

        // Test hook execution
        try {
            $hook_output = $this->module->hookDisplayProductExtraContent(array());
            if ($hook_output !== false) {
                $this->addSuccess("hookDisplayProductExtraContent executed successfully");
            } else {
                $this->addError("hookDisplayProductExtraContent returned false");
            }
        } catch (Exception $e) {
            $this->addError("Error executing hookDisplayProductExtraContent: " . $e->getMessage());
        }

        echo "</div>\n";
    }

    /**
     * Test front office functionality
     */
    private function testFrontOffice()
    {
        echo "<div class='test-section'><h2>Front Office Tests</h2>\n";

        // Test template file existence
        $template_file = dirname(__FILE__) . '/../views/templates/hook/equivalent_products.tpl';
        if (file_exists($template_file)) {
            $this->addSuccess("Template file exists: equivalent_products.tpl");
            
            // Check template content
            $template_content = file_get_contents($template_file);
            if (strpos($template_content, 'stequivalent-block') !== false) {
                $this->addSuccess("Template contains expected CSS classes");
            } else {
                $this->addError("Template missing expected CSS classes");
            }
        } else {
            $this->addError("Template file missing: equivalent_products.tpl");
        }

        // Test CSS file
        $css_file = dirname(__FILE__) . '/../views/css/stequivalent.css';
        if (file_exists($css_file)) {
            $this->addSuccess("CSS file exists: stequivalent.css");
        } else {
            $this->addError("CSS file missing: stequivalent.css");
        }

        // Test JavaScript file
        $js_file = dirname(__FILE__) . '/../views/js/stequivalent.js';
        if (file_exists($js_file)) {
            $this->addSuccess("JavaScript file exists: stequivalent.js");
        } else {
            $this->addError("JavaScript file missing: stequivalent.js");
        }

        // Test AJAX handler
        $ajax_file = dirname(__FILE__) . '/../ajax.php';
        if (file_exists($ajax_file)) {
            $this->addSuccess("AJAX handler exists: ajax.php");
        } else {
            $this->addError("AJAX handler missing: ajax.php");
        }

        echo "</div>\n";
    }

    /**
     * Add success message
     */
    private function addSuccess($message)
    {
        $this->successes[] = $message;
        echo "<div class='test-result success'>✓ $message</div>\n";
    }

    /**
     * Add error message
     */
    private function addError($message)
    {
        $this->errors[] = $message;
        echo "<div class='test-result error'>✗ $message</div>\n";
    }

    /**
     * Display final results
     */
    private function displayResults()
    {
        echo "<div class='test-section'><h2>Test Results Summary</h2>\n";
        
        $total_tests = count($this->successes) + count($this->errors);
        $success_rate = $total_tests > 0 ? (count($this->successes) / $total_tests) * 100 : 0;
        
        echo "<p><strong>Total Tests:</strong> $total_tests</p>\n";
        echo "<p><strong>Passed:</strong> <span class='success'>" . count($this->successes) . "</span></p>\n";
        echo "<p><strong>Failed:</strong> <span class='error'>" . count($this->errors) . "</span></p>\n";
        echo "<p><strong>Success Rate:</strong> " . round($success_rate, 2) . "%</p>\n";
        
        if (count($this->errors) === 0) {
            echo "<p class='success'><strong>🎉 All tests passed! Module is working correctly.</strong></p>\n";
        } else {
            echo "<p class='error'><strong>⚠️ Some tests failed. Please review the errors above.</strong></p>\n";
        }
        
        echo "</div>\n";
    }
}

// Run tests if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'TestModule.php') {
    $tester = new TestModule();
    $tester->runAllTests();
}
