<?php
/**
 * AJAX handler for Equivalent Product Types Module
 *
 * <AUTHOR>
 * @version 1.0.0
 */

require_once dirname(__FILE__) . '/../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../init.php';

// Load the module (which handles autoloading)
require_once dirname(__FILE__) . '/stequivalentproducttype.php';

// Check if module is active
$module = new StEquivalentProductType();
if (!$module->active) {
    http_response_code(404);
    exit;
}

// Get action
$action = Tools::getValue('action');

// Set JSON header
header('Content-Type: application/json');

try {
    switch ($action) {
        case 'loadMore':
            handleLoadMore();
            break;
            
        case 'updateStock':
            handleUpdateStock();
            break;
            
        case 'getAnalytics':
            handleGetAnalytics();
            break;
            
        case 'clearCache':
            handleClearCache();
            break;
            
        default:
            throw new Exception('Invalid action');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(array(
        'success' => false,
        'error' => $e->getMessage()
    ));
}

/**
 * Handle load more products request
 */
function handleLoadMore()
{
    $id_product = (int)Tools::getValue('id_product');
    $page = (int)Tools::getValue('page', 1);
    $id_product_attribute = (int)Tools::getValue('id_product_attribute');
    
    if (!$id_product) {
        throw new Exception('Product ID required');
    }
    
    require_once dirname(__FILE__) . '/src/Service/EquivalentProductService.php';
    $service = new \StEquivalentProductType\Service\EquivalentProductService();
    
    $equivalent_products = $service->findEquivalentProducts(
        $id_product,
        $id_product_attribute ?: null,
        Context::getContext()->language->id,
        Context::getContext()->shop->id
    );
    
    // Simple pagination (skip first page products)
    $per_page = 8;
    $offset = ($page - 1) * $per_page;
    $products = array_slice($equivalent_products, $offset, $per_page);
    
    if (empty($products)) {
        echo json_encode(array(
            'success' => true,
            'html' => '',
            'hasMore' => false
        ));
        return;
    }
    
    // Generate HTML for products
    $html = '';
    foreach ($products as $product) {
        $html .= generateProductHTML($product);
    }
    
    $hasMore = count($equivalent_products) > ($offset + $per_page);
    
    echo json_encode(array(
        'success' => true,
        'html' => $html,
        'hasMore' => $hasMore
    ));
}

/**
 * Handle stock update request
 */
function handleUpdateStock()
{
    $id_product = (int)Tools::getValue('id_product');
    $id_product_attribute = (int)Tools::getValue('id_product_attribute');
    
    if (!$id_product) {
        throw new Exception('Product ID required');
    }
    
    // Get current stock
    if ($id_product_attribute) {
        $stock = Db::getInstance()->getValue('
            SELECT quantity 
            FROM ' . _DB_PREFIX_ . 'product_attribute 
            WHERE id_product_attribute = ' . (int)$id_product_attribute
        );
    } else {
        $stock = Db::getInstance()->getValue('
            SELECT quantity 
            FROM ' . _DB_PREFIX_ . 'product 
            WHERE id_product = ' . (int)$id_product
        );
    }
    
    echo json_encode(array(
        'success' => true,
        'stock' => (int)$stock,
        'in_stock' => $stock > 0
    ));
}

/**
 * Handle analytics request
 */
function handleGetAnalytics()
{
    $id_product = (int)Tools::getValue('id_product');
    $id_product_attribute = (int)Tools::getValue('id_product_attribute');
    $period_days = (int)Tools::getValue('period_days', 30);
    
    if (!$id_product) {
        throw new Exception('Product ID required');
    }
    
    require_once dirname(__FILE__) . '/src/Service/AnalyticsService.php';
    $service = new \StEquivalentProductType\Service\AnalyticsService();
    
    $analytics = $service->getEquivalentProductsAnalytics(
        $id_product,
        $id_product_attribute ?: null,
        $period_days
    );
    
    echo json_encode(array(
        'success' => true,
        'analytics' => $analytics
    ));
}

/**
 * Handle cache clear request
 */
function handleClearCache()
{
    if (!Tools::isSubmit('token') || !Tools::getAdminTokenLite('AdminModules')) {
        throw new Exception('Invalid token');
    }
    
    // Clear sales cache
    Db::getInstance()->execute('TRUNCATE TABLE ' . _DB_PREFIX_ . 'stequivalent_sales_cache');
    
    echo json_encode(array(
        'success' => true,
        'message' => 'Cache cleared successfully'
    ));
}

/**
 * Generate HTML for a single product
 */
function generateProductHTML($product)
{
    $context = Context::getContext();
    $link = $context->link;
    
    $html = '<div class="col-xs-6 col-sm-4 col-md-3 stequivalent-product-col">';
    $html .= '<div class="stequivalent-product-item grid-item">';
    
    // Image
    if (Configuration::get('STEQUIVALENT_SHOW_IMAGES') && !empty($product['images'])) {
        $html .= '<div class="stequivalent-item-image">';
        $html .= '<a href="' . $link->getProductLink($product['id_product'], null, null, null, null, null, $product['id_product_attribute']) . '">';
        $html .= '<img src="' . $link->getImageLink($product['reference'], $product['images'][0]['id_image'], 'home_default') . '" ';
        $html .= 'alt="' . htmlspecialchars($product['name']) . '" class="stequivalent-product-image" />';
        $html .= '</a>';
        $html .= '</div>';
    }
    
    $html .= '<div class="stequivalent-item-content">';
    
    // Name
    $html .= '<h5 class="stequivalent-item-name">';
    $html .= '<a href="' . $link->getProductLink($product['id_product'], null, null, null, null, null, $product['id_product_attribute']) . '" class="stequivalent-product-link">';
    $html .= htmlspecialchars(Tools::truncate($product['name'], 50));
    $html .= '</a>';
    $html .= '</h5>';
    
    // Attributes
    if (!empty($product['attributes'])) {
        $html .= '<div class="stequivalent-item-attributes">';
        foreach ($product['attributes'] as $index => $attr) {
            $html .= '<small class="stequivalent-attribute">';
            $html .= htmlspecialchars($attr['attribute_name']);
            if ($index < count($product['attributes']) - 1) {
                $html .= ', ';
            }
            $html .= '</small>';
        }
        $html .= '</div>';
    }
    
    // Price
    if (Configuration::get('STEQUIVALENT_SHOW_PRICE')) {
        $html .= '<div class="stequivalent-item-price">';
        $html .= '<strong>' . Tools::displayPrice($product['final_price']) . '</strong>';
        $html .= '</div>';
    }
    
    // Stock
    if (Configuration::get('STEQUIVALENT_SHOW_STOCK')) {
        $html .= '<div class="stequivalent-item-stock">';
        if ($product['stock_info']['in_stock']) {
            $html .= '<span class="stock-badge in-stock">';
            $html .= '<i class="icon-check"></i> Available';
        } else {
            $html .= '<span class="stock-badge out-of-stock">';
            $html .= '<i class="icon-times"></i> Out of Stock';
        }
        $html .= '</span>';
        $html .= '</div>';
    }
    
    // Sales
    if (Configuration::get('STEQUIVALENT_SHOW_SALES') && !empty($product['sales_info'])) {
        $html .= '<div class="stequivalent-item-sales">';
        $html .= '<small>';
        $html .= '<i class="icon-shopping-cart"></i> ';
        $html .= (int)$product['sales_info']['total_quantity'] . ' sold';
        $html .= '</small>';
        $html .= '</div>';
    }
    
    // Action button
    $html .= '<div class="stequivalent-item-actions">';
    $html .= '<a href="' . $link->getProductLink($product['id_product'], null, null, null, null, null, $product['id_product_attribute']) . '" ';
    $html .= 'class="btn btn-primary btn-sm btn-block stequivalent-view-btn">View</a>';
    $html .= '</div>';
    
    $html .= '</div>'; // content
    $html .= '</div>'; // item
    $html .= '</div>'; // col
    
    return $html;
}
