/**
 * CSS for Equivalent Product Types Module
 *
 * <AUTHOR>
 * @version 1.0.0
 */

/* Main container */
.stequivalent-block {
    margin: 20px 0;
    padding: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stequivalent-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007cbb;
}

.stequivalent-title i {
    color: #007cbb;
    margin-right: 8px;
}

.stequivalent-count {
    color: #666;
    font-weight: normal;
    font-size: 14px;
}

.stequivalent-description {
    color: #666;
    margin-bottom: 20px;
    font-style: italic;
}

/* Table Display Mode */
.stequivalent-table-container {
    overflow-x: auto;
}

.stequivalent-table {
    width: 100%;
    margin-bottom: 0;
}

.stequivalent-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    border-bottom: 2px solid #dee2e6;
    padding: 12px 8px;
    text-align: center;
}

.stequivalent-table td {
    padding: 12px 8px;
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
}

.stequivalent-th-image,
.stequivalent-td-image {
    width: 80px;
    text-align: center;
}

.stequivalent-th-name {
    min-width: 200px;
}

.stequivalent-th-attributes {
    min-width: 150px;
}

.stequivalent-th-price,
.stequivalent-td-price {
    width: 100px;
    text-align: center;
}

.stequivalent-th-stock,
.stequivalent-td-stock {
    width: 120px;
    text-align: center;
}

.stequivalent-th-sales,
.stequivalent-td-sales {
    width: 100px;
    text-align: center;
}

.stequivalent-th-action,
.stequivalent-td-action {
    width: 80px;
    text-align: center;
}

/* Product Images */
.stequivalent-product-image {
    max-width: 60px;
    max-height: 60px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.stequivalent-no-image {
    width: 60px;
    height: 60px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 24px;
}

/* Product Links */
.stequivalent-product-link {
    color: #007cbb;
    text-decoration: none;
    font-weight: 500;
}

.stequivalent-product-link:hover {
    color: #005a8b;
    text-decoration: underline;
}

.stequivalent-reference {
    color: #666;
    font-style: italic;
}

/* Attributes */
.stequivalent-attributes {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.stequivalent-attribute {
    display: inline-block;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 5px;
    margin-bottom: 3px;
}

.stequivalent-attribute-badge {
    display: inline-block;
    background: #007cbb;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    margin-right: 5px;
    margin-bottom: 3px;
}

.stequivalent-no-attributes {
    color: #999;
    font-style: italic;
}

/* Price */
.stequivalent-price {
    font-size: 16px;
    font-weight: bold;
    color: #28a745;
}

/* Stock Status */
.stequivalent-stock.in-stock,
.stock-status.in-stock,
.stock-badge.in-stock {
    color: #28a745;
}

.stequivalent-stock.out-of-stock,
.stock-status.out-of-stock,
.stock-badge.out-of-stock {
    color: #dc3545;
}

.stock-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
}

.stock-badge.in-stock {
    background: #d4edda;
    border: 1px solid #c3e6cb;
}

.stock-badge.out-of-stock {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
}

/* Sales Info */
.stequivalent-sales {
    text-align: center;
}

.sales-quantity {
    font-weight: bold;
    color: #333;
}

.sales-period {
    color: #666;
    font-style: italic;
}

.stequivalent-no-sales {
    color: #999;
    font-style: italic;
}

/* Buttons */
.stequivalent-view-btn {
    background: #007cbb;
    border-color: #007cbb;
    color: white;
    font-size: 12px;
    padding: 4px 8px;
}

.stequivalent-view-btn:hover {
    background: #005a8b;
    border-color: #005a8b;
    color: white;
}

/* List Display Mode */
.stequivalent-list-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stequivalent-product-item.list-item {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background: #fff;
    transition: box-shadow 0.3s ease;
}

.stequivalent-product-item.list-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stequivalent-item-content {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.stequivalent-item-image {
    flex-shrink: 0;
}

.stequivalent-item-details {
    flex: 1;
}

.stequivalent-item-name {
    margin: 0 0 10px 0;
    font-size: 16px;
}

.stequivalent-item-reference {
    margin-bottom: 8px;
    color: #666;
    font-size: 13px;
}

.stequivalent-item-attributes {
    margin-bottom: 10px;
}

.stequivalent-item-info {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 10px;
}

.stequivalent-item-price {
    font-size: 18px;
    font-weight: bold;
    color: #28a745;
}

.stequivalent-item-stock,
.stequivalent-item-sales {
    font-size: 13px;
}

/* Grid Display Mode */
.stequivalent-grid-container {
    margin: 0 -7.5px;
}

.stequivalent-product-col {
    padding: 0 7.5px;
    margin-bottom: 15px;
}

.stequivalent-product-item.grid-item {
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.stequivalent-product-item.grid-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stequivalent-product-item.grid-item .stequivalent-item-image {
    text-align: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.stequivalent-product-item.grid-item .stequivalent-product-image {
    max-width: 100%;
    max-height: 150px;
}

.stequivalent-product-item.grid-item .stequivalent-item-content {
    padding: 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.stequivalent-product-item.grid-item .stequivalent-item-name {
    margin: 0 0 8px 0;
    font-size: 14px;
    line-height: 1.3;
}

.stequivalent-product-item.grid-item .stequivalent-item-attributes {
    margin-bottom: 8px;
    min-height: 20px;
}

.stequivalent-product-item.grid-item .stequivalent-item-price {
    margin-bottom: 8px;
    font-size: 16px;
}

.stequivalent-product-item.grid-item .stequivalent-item-stock {
    margin-bottom: 8px;
}

.stequivalent-product-item.grid-item .stequivalent-item-sales {
    margin-bottom: 10px;
    color: #666;
}

.stequivalent-product-item.grid-item .stequivalent-item-actions {
    margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stequivalent-block {
        margin: 10px 0;
        padding: 15px;
    }
    
    .stequivalent-table-container {
        font-size: 12px;
    }
    
    .stequivalent-table th,
    .stequivalent-table td {
        padding: 8px 4px;
    }
    
    .stequivalent-product-image {
        max-width: 40px;
        max-height: 40px;
    }
    
    .stequivalent-no-image {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
    
    .stequivalent-item-content {
        flex-direction: column;
        gap: 10px;
    }
    
    .stequivalent-item-info {
        flex-direction: column;
        gap: 5px;
    }
}

@media (max-width: 480px) {
    .stequivalent-product-col {
        width: 50%;
        float: left;
    }
    
    .stequivalent-title {
        font-size: 16px;
    }
    
    .stequivalent-attribute-badge {
        font-size: 10px;
        padding: 2px 6px;
    }
}
