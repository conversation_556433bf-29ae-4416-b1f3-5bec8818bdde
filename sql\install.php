<?php
/**
 * Installation SQL for Equivalent Product Types Module
 *
 * <AUTHOR>
 * @version 1.0.0
 */

$sql = array();

// Table to store equivalent product relationships
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stequivalent_product_groups` (
    `id_group` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `group_hash` varchar(32) NOT NULL,
    `attributes_hash` varchar(255) NOT NULL,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_group`),
    UNIQUE KEY `group_hash` (`group_hash`),
    KEY `attributes_hash` (`attributes_hash`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table to link products to equivalent groups
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stequivalent_product_group_products` (
    `id_group` int(10) unsigned NOT NULL,
    `id_product` int(10) unsigned NOT NULL,
    `id_product_attribute` int(10) unsigned DEFAULT NULL,
    `date_add` datetime NOT NULL,
    PRIMARY KEY (`id_group`, `id_product`),
    KEY `id_product` (`id_product`),
    KEY `id_product_attribute` (`id_product_attribute`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table to store cached sales data for performance
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stequivalent_sales_cache` (
    `id_product` int(10) unsigned NOT NULL,
    `id_product_attribute` int(10) unsigned DEFAULT NULL,
    `period_days` int(3) unsigned NOT NULL DEFAULT 30,
    `total_sales` int(10) unsigned NOT NULL DEFAULT 0,
    `total_quantity` int(10) unsigned NOT NULL DEFAULT 0,
    `last_updated` datetime NOT NULL,
    PRIMARY KEY (`id_product`, `period_days`),
    KEY `id_product_attribute` (`id_product_attribute`),
    KEY `last_updated` (`last_updated`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table to store module configuration per shop
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stequivalent_configuration` (
    `id_configuration` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_shop` int(10) unsigned NOT NULL DEFAULT 1,
    `id_shop_group` int(10) unsigned DEFAULT NULL,
    `name` varchar(32) NOT NULL,
    `value` text,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_configuration`),
    UNIQUE KEY `name_shop` (`name`, `id_shop`),
    KEY `id_shop` (`id_shop`),
    KEY `id_shop_group` (`id_shop_group`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table to store attribute mapping for equivalent detection
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stequivalent_attribute_mapping` (
    `id_mapping` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_attribute_group` int(10) unsigned NOT NULL,
    `attribute_name` varchar(128) NOT NULL,
    `is_enabled` tinyint(1) unsigned NOT NULL DEFAULT 1,
    `weight` int(3) unsigned NOT NULL DEFAULT 1,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_mapping`),
    UNIQUE KEY `id_attribute_group` (`id_attribute_group`),
    KEY `is_enabled` (`is_enabled`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

foreach ($sql as $query) {
    if (Db::getInstance()->execute($query) == false) {
        return false;
    }
}

// Insert default attribute mappings
$default_attributes = array(
    array('name' => 'Color', 'weight' => 3),
    array('name' => 'Size', 'weight' => 2),
    array('name' => 'Material', 'weight' => 1),
);

foreach ($default_attributes as $attr) {
    // Try to find existing attribute group
    $id_attribute_group = Db::getInstance()->getValue('
        SELECT ag.id_attribute_group 
        FROM ' . _DB_PREFIX_ . 'attribute_group_lang agl
        LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group ag ON ag.id_attribute_group = agl.id_attribute_group
        WHERE agl.name LIKE "%' . pSQL($attr['name']) . '%" 
        AND agl.id_lang = ' . (int)Configuration::get('PS_LANG_DEFAULT') . '
        LIMIT 1
    ');
    
    if ($id_attribute_group) {
        Db::getInstance()->insert('stequivalent_attribute_mapping', array(
            'id_attribute_group' => (int)$id_attribute_group,
            'attribute_name' => pSQL($attr['name']),
            'is_enabled' => 1,
            'weight' => (int)$attr['weight'],
            'date_add' => date('Y-m-d H:i:s'),
            'date_upd' => date('Y-m-d H:i:s'),
        ), false, true, Db::INSERT_IGNORE);
    }
}
