<?php
/**
 * English translations for Equivalent Product Types Module
 *
 * <AUTHOR>
 * @version 1.0.0
 */

global $_MODULE;
$_MODULE = array();

// Module information
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_'] = 'Equivalent Product Types';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_description'] = 'Display all product variants with similar specifications (color, size, reference, etc.) to the currently viewed product directly on the product page.';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_confirm_uninstall'] = 'Are you sure you want to uninstall this module?';

// Configuration
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_settings'] = 'Settings';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_enable_module'] = 'Enable Module';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_enable_disable_desc'] = 'Enable or disable the equivalent product types module';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_enabled'] = 'Enabled';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_disabled'] = 'Disabled';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_yes'] = 'Yes';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_no'] = 'No';

// Attributes
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_attributes_to_match'] = 'Attributes to Match';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_attributes_desc'] = 'Select which attributes should be used to determine equivalent specifications';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_color'] = 'Color';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_size'] = 'Size';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_reference'] = 'Reference';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_material'] = 'Material';

// Display options
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_display_mode'] = 'Display Mode';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_display_mode_desc'] = 'Choose how to display equivalent products';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_grid'] = 'Grid';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_list'] = 'List';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_table'] = 'Table';

$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_show_product_images'] = 'Show Product Images';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_show_images_desc'] = 'Display product images for equivalent products';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_show_prices'] = 'Show Prices';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_show_prices_desc'] = 'Display prices for equivalent products';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_show_stock_information'] = 'Show Stock Information';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_show_stock_desc'] = 'Display stock availability for equivalent products';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_show_sales_information'] = 'Show Sales Information';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_show_sales_desc'] = 'Display sales data for equivalent products';

// Sales configuration
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_sales_period_days'] = 'Sales Period (days)';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_sales_period_desc'] = 'Number of days to calculate sales data (e.g., 30 for last 30 days)';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_hide_out_of_stock'] = 'Hide Out of Stock Products';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_hide_out_of_stock_desc'] = 'Hide equivalent products that are out of stock';

// Form actions
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_save'] = 'Save';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_back_to_list'] = 'Back to list';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_settings_updated'] = 'Settings updated';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_invalid_configuration'] = 'Invalid Configuration value';

// Front office
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_similar_products'] = 'Similar Products';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_discover_similar'] = 'Discover other products with similar specifications:';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_image'] = 'Image';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_product'] = 'Product';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_specifications'] = 'Specifications';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_price'] = 'Price';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_stock'] = 'Stock';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_sales'] = 'Sales';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_action'] = 'Action';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_view'] = 'View';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_view_product'] = 'View Product';

// Stock status
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_in_stock'] = 'In Stock';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_out_of_stock'] = 'Out of Stock';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_available'] = 'Available';

// Sales information
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_sold'] = 'sold';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_last_days'] = 'Last %d days';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_sold_in_last_days'] = 'sold in last %d days';

// Admin interface
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_attribute_mapping'] = 'Attribute Mapping';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_attribute_name'] = 'Attribute Name';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_attribute_group'] = 'Attribute Group';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_weight'] = 'Weight';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_enabled'] = 'Enabled';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_date_added'] = 'Date Added';

$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_attribute_name_hint'] = 'Name of the attribute for display purposes';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_attribute_group_hint'] = 'Select the PrestaShop attribute group to map';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_weight_hint'] = 'Weight for matching priority (higher = more important)';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_enabled_hint'] = 'Enable or disable this attribute for equivalent product matching';

$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_fill_required_fields'] = 'Please fill all required fields';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_mapping_saved'] = 'Attribute mapping saved successfully';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_error_saving'] = 'Error saving attribute mapping';

// Statistics
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_module_statistics'] = 'Module Statistics';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_equivalent_groups'] = 'Equivalent Groups';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_products_with_equivalents'] = 'Products with Equivalents';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_cache_entries'] = 'Cache Entries';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_last_cache_update'] = 'Last Cache Update';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_never'] = 'Never';

$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_popular_groups'] = 'Popular Equivalent Groups';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_group_hash'] = 'Group Hash';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_product_count'] = 'Product Count';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_no_groups_found'] = 'No equivalent groups found yet.';

$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_attribute_configuration'] = 'Attribute Configuration';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_status'] = 'Status';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_no_mappings'] = 'No attribute mappings configured yet.';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_add_attribute_mapping'] = 'Add Attribute Mapping';

$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_module_information'] = 'Module Information';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_how_it_works'] = 'How it works:';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_how_it_works_1'] = 'The module automatically detects products with similar specifications based on configured attributes.';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_how_it_works_2'] = 'Products are grouped by matching attribute values (color, size, reference, etc.).';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_how_it_works_3'] = 'Sales data is cached for performance and updated hourly.';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_how_it_works_4'] = 'The display can be customized through the main module configuration.';

$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_performance_tips'] = 'Performance Tips:';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_performance_tip_1'] = 'Use attribute weights to prioritize important matching criteria.';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_performance_tip_2'] = 'Disable unused attributes to improve matching performance.';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_performance_tip_3'] = 'The sales cache is automatically maintained but can be cleared if needed.';

// Admin actions
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_add_new_mapping'] = 'Add new attribute mapping';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_view_statistics'] = 'View Statistics';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_delete_selected'] = 'Delete selected';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_delete_confirm'] = 'Delete selected items?';

// Errors and messages
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_no_name_provided'] = 'No name provided';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_configuration_updated'] = 'Configuration updated successfully';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_error_occurred'] = 'An error occurred';

// Additional front office elements
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_reference_label'] = 'Reference:';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_show_all'] = 'All';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_show_in_stock'] = 'In Stock';
$_MODULE['<{stequivalentproducttype}prestashop>stequivalentproducttype_show_out_of_stock'] = 'Out of Stock';
