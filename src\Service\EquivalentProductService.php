<?php
/**
 * Service for detecting and managing equivalent products
 *
 * <AUTHOR>
 * @version 1.0.0
 */

namespace StEquivalentProductType\Service;

use Db;
use Product;
use Configuration;
use Context;

class EquivalentProductService
{
    /**
     * Find equivalent products for a given product
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $id_lang
     * @param int $id_shop
     * @return array
     */
    public function findEquivalentProducts($id_product, $id_product_attribute = null, $id_lang = null, $id_shop = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        // Get enabled attributes for matching
        $enabled_attributes = json_decode(Configuration::get('STEQUIVALENT_ATTRIBUTES'), true);
        if (!$enabled_attributes || empty($enabled_attributes)) {
            return array();
        }

        // Get product attributes
        $product_attributes = $this->getProductAttributes($id_product, $id_product_attribute, $enabled_attributes);
        if (empty($product_attributes)) {
            return array();
        }

        // Find products with matching attributes
        $equivalent_products = $this->findProductsByAttributes($product_attributes, $id_product, $id_lang, $id_shop);

        // Enhance with additional data
        return $this->enhanceProductData($equivalent_products, $id_lang, $id_shop);
    }

    /**
     * Get product attributes for matching
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param array $enabled_attributes
     * @return array
     */
    protected function getProductAttributes($id_product, $id_product_attribute, $enabled_attributes)
    {
        $attributes = array();

        // Get product reference if enabled
        if (in_array('reference', $enabled_attributes)) {
            $reference = Db::getInstance()->getValue('
                SELECT reference 
                FROM ' . _DB_PREFIX_ . 'product 
                WHERE id_product = ' . (int)$id_product
            );
            if ($reference) {
                $attributes['reference'] = $reference;
            }
        }

        // Get product attributes (color, size, etc.)
        if ($id_product_attribute) {
            $sql = '
                SELECT ag.id_attribute_group, agl.name as group_name, a.id_attribute, al.name as attribute_name
                FROM ' . _DB_PREFIX_ . 'product_attribute_combination pac
                LEFT JOIN ' . _DB_PREFIX_ . 'attribute a ON a.id_attribute = pac.id_attribute
                LEFT JOIN ' . _DB_PREFIX_ . 'attribute_lang al ON al.id_attribute = a.id_attribute
                LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group ag ON ag.id_attribute_group = a.id_attribute_group
                LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group_lang agl ON agl.id_attribute_group = ag.id_attribute_group
                WHERE pac.id_product_attribute = ' . (int)$id_product_attribute . '
                AND al.id_lang = ' . (int)Context::getContext()->language->id . '
                AND agl.id_lang = ' . (int)Context::getContext()->language->id;

            $product_attrs = Db::getInstance()->executeS($sql);
            
            foreach ($product_attrs as $attr) {
                $group_name = strtolower($attr['group_name']);
                if (in_array($group_name, $enabled_attributes)) {
                    $attributes[$group_name] = $attr['attribute_name'];
                }
            }
        }

        return $attributes;
    }

    /**
     * Find products by matching attributes
     *
     * @param array $attributes
     * @param int $exclude_product_id
     * @param int $id_lang
     * @param int $id_shop
     * @return array
     */
    protected function findProductsByAttributes($attributes, $exclude_product_id, $id_lang, $id_shop)
    {
        $products = array();
        
        // Build base query
        $sql = '
            SELECT DISTINCT p.id_product, pa.id_product_attribute, pl.name, p.reference,
                   p.price, pa.price as attribute_price, p.active, pa.minimal_quantity as quantity
            FROM ' . _DB_PREFIX_ . 'product p
            LEFT JOIN ' . _DB_PREFIX_ . 'product_lang pl ON pl.id_product = p.id_product
            LEFT JOIN ' . _DB_PREFIX_ . 'product_attribute pa ON pa.id_product = p.id_product
            LEFT JOIN ' . _DB_PREFIX_ . 'product_shop ps ON ps.id_product = p.id_product
            WHERE p.id_product != ' . (int)$exclude_product_id . '
            AND pl.id_lang = ' . (int)$id_lang . '
            AND ps.id_shop = ' . (int)$id_shop . '
            AND p.active = 1';

        $conditions = array();

        // Add reference condition if specified
        if (isset($attributes['reference']) && !empty($attributes['reference'])) {
            $conditions[] = 'p.reference = "' . pSQL($attributes['reference']) . '"';
        }

        // Add attribute conditions
        foreach ($attributes as $attr_type => $attr_value) {
            if ($attr_type === 'reference') {
                continue; // Already handled above
            }

            $conditions[] = 'EXISTS (
                SELECT 1 
                FROM ' . _DB_PREFIX_ . 'product_attribute_combination pac2
                LEFT JOIN ' . _DB_PREFIX_ . 'attribute a2 ON a2.id_attribute = pac2.id_attribute
                LEFT JOIN ' . _DB_PREFIX_ . 'attribute_lang al2 ON al2.id_attribute = a2.id_attribute
                LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group ag2 ON ag2.id_attribute_group = a2.id_attribute_group
                LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group_lang agl2 ON agl2.id_attribute_group = ag2.id_attribute_group
                WHERE pac2.id_product_attribute = pa.id_product_attribute
                AND al2.name = "' . pSQL($attr_value) . '"
                AND agl2.name LIKE "%' . pSQL(ucfirst($attr_type)) . '%"
                AND al2.id_lang = ' . (int)$id_lang . '
                AND agl2.id_lang = ' . (int)$id_lang . '
            )';
        }

        if (!empty($conditions)) {
            $sql .= ' AND (' . implode(' OR ', $conditions) . ')';
        }

        // Hide out of stock if configured
        if (Configuration::get('STEQUIVALENT_HIDE_OUT_OF_STOCK')) {
            $sql .= ' AND (pa.quantity > 0 OR pa.id_product_attribute IS NULL)';
        }

        $sql .= ' ORDER BY pl.name ASC LIMIT 20';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Enhance product data with additional information
     *
     * @param array $products
     * @param int $id_lang
     * @param int $id_shop
     * @return array
     */
    protected function enhanceProductData($products, $id_lang, $id_shop)
    {
        if (empty($products)) {
            return array();
        }

        foreach ($products as &$product) {
            // Get product images
            if (Configuration::get('STEQUIVALENT_SHOW_IMAGES')) {
                $product['images'] = $this->getProductImages($product['id_product'], $product['id_product_attribute']);
            }

            // Get stock information
            if (Configuration::get('STEQUIVALENT_SHOW_STOCK')) {
                $product['stock_info'] = $this->getStockInfo($product['id_product'], $product['id_product_attribute']);
            }

            // Get sales information
            if (Configuration::get('STEQUIVALENT_SHOW_SALES')) {
                $sales_period = (int)Configuration::get('STEQUIVALENT_SALES_PERIOD') ?: 30;
                $product['sales_info'] = $this->getSalesInfo($product['id_product'], $product['id_product_attribute'], $sales_period);
            }

            // Calculate final price
            $product['final_price'] = $product['price'];
            if ($product['attribute_price']) {
                $product['final_price'] += $product['attribute_price'];
            }

            // Get product attributes for display
            $product['attributes'] = $this->getProductAttributesForDisplay($product['id_product'], $product['id_product_attribute'], $id_lang);
        }

        return $products;
    }

    /**
     * Get product images
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @return array
     */
    protected function getProductImages($id_product, $id_product_attribute = null)
    {
        $images = array();
        
        // Get specific attribute image if available
        if ($id_product_attribute) {
            $sql = '
                SELECT i.id_image, i.cover
                FROM ' . _DB_PREFIX_ . 'product_attribute_image pai
                LEFT JOIN ' . _DB_PREFIX_ . 'image i ON i.id_image = pai.id_image
                WHERE pai.id_product_attribute = ' . (int)$id_product_attribute . '
                ORDER BY i.cover DESC, i.position ASC
                LIMIT 1';
            
            //$attribute_image = Db::getInstance()->getRow($sql);
			$attribute_image = '';
            if ($attribute_image) {
                $images[] = $attribute_image;
            }
        }

        // Get main product image if no attribute image
        if (empty($images)) {
            $sql = '
                SELECT id_image, cover
                FROM ' . _DB_PREFIX_ . 'image
                WHERE id_product = ' . (int)$id_product . '
                ORDER BY cover DESC, position ASC
                LIMIT 1';
            
            $main_image = Db::getInstance()->getRow($sql);
            if ($main_image) {
                $images[] = $main_image;
            }
        }

        return $images;
    }

    /**
     * Get stock information
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @return array
     */
    protected function getStockInfo($id_product, $id_product_attribute = null)
    {
        if ($id_product_attribute) {
            $quantity = Db::getInstance()->getValue('
                SELECT quantity 
                FROM ' . _DB_PREFIX_ . 'product_attribute 
                WHERE id_product_attribute = ' . (int)$id_product_attribute
            );
        } else {
            $quantity = Db::getInstance()->getValue('
                SELECT quantity 
                FROM ' . _DB_PREFIX_ . 'product 
                WHERE id_product = ' . (int)$id_product
            );
        }

        return array(
            'quantity' => (int)$quantity,
            'in_stock' => $quantity > 0,
            'stock_status' => $quantity > 0 ? 'in_stock' : 'out_of_stock'
        );
    }

    /**
     * Get sales information
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $period_days
     * @return array
     */
    protected function getSalesInfo($id_product, $id_product_attribute = null, $period_days = 30)
    {
        // Check cache first
        $cache_key = $id_product . '_' . ($id_product_attribute ?: 0) . '_' . $period_days;
        $cached = $this->getSalesCacheData($id_product, $id_product_attribute, $period_days);
        
        if ($cached && strtotime($cached['last_updated']) > strtotime('-1 hour')) {
            return array(
                'total_sales' => (int)$cached['total_sales'],
                'total_quantity' => (int)$cached['total_quantity'],
                'period_days' => $period_days
            );
        }

        // Calculate fresh data
        $date_from = date('Y-m-d H:i:s', strtotime('-' . $period_days . ' days'));
        
        $sql = '
            SELECT COUNT(od.id_order_detail) as total_sales, SUM(od.product_quantity) as total_quantity
            FROM ' . _DB_PREFIX_ . 'order_detail od
            LEFT JOIN ' . _DB_PREFIX_ . 'orders o ON o.id_order = od.id_order
            WHERE od.product_id = ' . (int)$id_product . '
            AND o.date_add >= "' . pSQL($date_from) . '"
            AND o.valid = 1';

        if ($id_product_attribute) {
            $sql .= ' AND od.product_attribute_id = ' . (int)$id_product_attribute;
        }

        $result = Db::getInstance()->getRow($sql);
        
        $sales_data = array(
            'total_sales' => (int)($result['total_sales'] ?: 0),
            'total_quantity' => (int)($result['total_quantity'] ?: 0),
            'period_days' => $period_days
        );

        // Update cache
        $this->updateSalesCache($id_product, $id_product_attribute, $period_days, $sales_data);

        return $sales_data;
    }

    /**
     * Get product attributes for display
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $id_lang
     * @return array
     */
    protected function getProductAttributesForDisplay($id_product, $id_product_attribute, $id_lang)
    {
        if (!$id_product_attribute) {
            return array();
        }

        $sql = '
            SELECT agl.name as group_name, al.name as attribute_name
            FROM ' . _DB_PREFIX_ . 'product_attribute_combination pac
            LEFT JOIN ' . _DB_PREFIX_ . 'attribute a ON a.id_attribute = pac.id_attribute
            LEFT JOIN ' . _DB_PREFIX_ . 'attribute_lang al ON al.id_attribute = a.id_attribute
            LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group ag ON ag.id_attribute_group = a.id_attribute_group
            LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group_lang agl ON agl.id_attribute_group = ag.id_attribute_group
            WHERE pac.id_product_attribute = ' . (int)$id_product_attribute . '
            AND al.id_lang = ' . (int)$id_lang . '
            AND agl.id_lang = ' . (int)$id_lang;

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get sales cache data
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $period_days
     * @return array|false
     */
    protected function getSalesCacheData($id_product, $id_product_attribute, $period_days)
    {
        $sql = '
            SELECT total_sales, total_quantity, last_updated
            FROM ' . _DB_PREFIX_ . 'stequivalent_sales_cache
            WHERE id_product = ' . (int)$id_product . '
            AND period_days = ' . (int)$period_days;

        if ($id_product_attribute) {
            $sql .= ' AND id_product_attribute = ' . (int)$id_product_attribute;
        } else {
            $sql .= ' AND id_product_attribute IS NULL';
        }

        return Db::getInstance()->getRow($sql);
    }

    /**
     * Update sales cache
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $period_days
     * @param array $sales_data
     */
    protected function updateSalesCache($id_product, $id_product_attribute, $period_days, $sales_data)
    {
        $data = array(
            'id_product' => (int)$id_product,
            'id_product_attribute' => $id_product_attribute ? (int)$id_product_attribute : null,
            'period_days' => (int)$period_days,
            'total_sales' => (int)$sales_data['total_sales'],
            'total_quantity' => (int)$sales_data['total_quantity'],
            'last_updated' => date('Y-m-d H:i:s')
        );

        Db::getInstance()->insert('stequivalent_sales_cache', $data, false, true, Db::ON_DUPLICATE_KEY);
    }
}
