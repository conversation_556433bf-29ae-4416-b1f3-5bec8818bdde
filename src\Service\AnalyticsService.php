<?php
/**
 * Analytics Service for Equivalent Product Types Module
 *
 * <AUTHOR>
 * @version 1.0.0
 */

namespace StEquivalentProductType\Service;

use Db;
use Configuration;
use Context;

class AnalyticsService
{
    /**
     * Get comprehensive sales analytics for equivalent products
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $period_days
     * @return array
     */
    public function getEquivalentProductsAnalytics($id_product, $id_product_attribute = null, $period_days = 30)
    {
        require_once dirname(__FILE__) . '/EquivalentProductService.php';
        $equivalentService = new EquivalentProductService();
        
        // Get equivalent products
        $equivalent_products = $equivalentService->findEquivalentProducts(
            $id_product,
            $id_product_attribute,
            Context::getContext()->language->id,
            Context::getContext()->shop->id
        );

        if (empty($equivalent_products)) {
            return array();
        }

        $analytics = array();
        $total_sales = 0;
        $total_quantity = 0;
        $total_revenue = 0;

        foreach ($equivalent_products as $product) {
            $product_analytics = $this->getProductAnalytics(
                $product['id_product'],
                $product['id_product_attribute'],
                $period_days
            );

            $analytics[] = array_merge($product, $product_analytics);
            
            $total_sales += $product_analytics['sales_count'];
            $total_quantity += $product_analytics['quantity_sold'];
            $total_revenue += $product_analytics['revenue'];
        }

        return array(
            'products' => $analytics,
            'summary' => array(
                'total_sales' => $total_sales,
                'total_quantity' => $total_quantity,
                'total_revenue' => $total_revenue,
                'average_order_value' => $total_sales > 0 ? $total_revenue / $total_sales : 0,
                'period_days' => $period_days
            )
        );
    }

    /**
     * Get detailed analytics for a single product
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $period_days
     * @return array
     */
    public function getProductAnalytics($id_product, $id_product_attribute = null, $period_days = 30)
    {
        $date_from = date('Y-m-d H:i:s', strtotime('-' . $period_days . ' days'));
        
        // Base query for order details
        $base_sql = '
            FROM ' . _DB_PREFIX_ . 'order_detail od
            LEFT JOIN ' . _DB_PREFIX_ . 'orders o ON o.id_order = od.id_order
            WHERE od.product_id = ' . (int)$id_product . '
            AND o.date_add >= "' . pSQL($date_from) . '"
            AND o.valid = 1';

        if ($id_product_attribute) {
            $base_sql .= ' AND od.product_attribute_id = ' . (int)$id_product_attribute;
        }

        // Sales count and quantity
        $sales_data = Db::getInstance()->getRow('
            SELECT 
                COUNT(DISTINCT od.id_order) as sales_count,
                SUM(od.product_quantity) as quantity_sold,
                SUM(od.total_price_tax_incl) as revenue,
                AVG(od.total_price_tax_incl) as avg_order_value
            ' . $base_sql
        );

        // Daily sales trend
        $daily_sales = Db::getInstance()->executeS('
            SELECT 
                DATE(o.date_add) as sale_date,
                COUNT(DISTINCT od.id_order) as daily_sales,
                SUM(od.product_quantity) as daily_quantity,
                SUM(od.total_price_tax_incl) as daily_revenue
            ' . $base_sql . '
            GROUP BY DATE(o.date_add)
            ORDER BY sale_date ASC
        ');

        // Customer segments
        $customer_segments = $this->getCustomerSegments($id_product, $id_product_attribute, $period_days);

        // Stock movement
        $stock_movement = $this->getStockMovement($id_product, $id_product_attribute, $period_days);

        // Performance metrics
        $performance = $this->calculatePerformanceMetrics($id_product, $id_product_attribute, $period_days);

        return array(
            'sales_count' => (int)($sales_data['sales_count'] ?: 0),
            'quantity_sold' => (int)($sales_data['quantity_sold'] ?: 0),
            'revenue' => (float)($sales_data['revenue'] ?: 0),
            'avg_order_value' => (float)($sales_data['avg_order_value'] ?: 0),
            'daily_sales' => $daily_sales,
            'customer_segments' => $customer_segments,
            'stock_movement' => $stock_movement,
            'performance' => $performance,
            'period_days' => $period_days
        );
    }

    /**
     * Get customer segments for product sales
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $period_days
     * @return array
     */
    protected function getCustomerSegments($id_product, $id_product_attribute, $period_days)
    {
        $date_from = date('Y-m-d H:i:s', strtotime('-' . $period_days . ' days'));
        
        $sql = '
            SELECT 
                CASE 
                    WHEN c.id_customer IS NULL THEN "Guest"
                    ELSE "Registered"
                END as customer_type,
                COUNT(DISTINCT od.id_order) as orders,
                SUM(od.product_quantity) as quantity,
                SUM(od.total_price_tax_incl) as revenue
            FROM ' . _DB_PREFIX_ . 'order_detail od
            LEFT JOIN ' . _DB_PREFIX_ . 'orders o ON o.id_order = od.id_order
            LEFT JOIN ' . _DB_PREFIX_ . 'customer c ON c.id_customer = o.id_customer
            WHERE od.product_id = ' . (int)$id_product . '
            AND o.date_add >= "' . pSQL($date_from) . '"
            AND o.valid = 1';

        if ($id_product_attribute) {
            $sql .= ' AND od.product_attribute_id = ' . (int)$id_product_attribute;
        }

        $sql .= ' GROUP BY customer_type';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get stock movement data
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $period_days
     * @return array
     */
    protected function getStockMovement($id_product, $id_product_attribute, $period_days)
    {
        $date_from = date('Y-m-d H:i:s', strtotime('-' . $period_days . ' days'));
        
        // Get stock movements from stock_mvt table
        $sql = '
            SELECT 
                DATE(sm.date_add) as movement_date,
                SUM(CASE WHEN sm.sign = 1 THEN sm.physical_quantity ELSE 0 END) as stock_in,
                SUM(CASE WHEN sm.sign = -1 THEN ABS(sm.physical_quantity) ELSE 0 END) as stock_out,
                SUM(sm.physical_quantity) as net_movement
            FROM ' . _DB_PREFIX_ . 'stock_mvt sm
            WHERE sm.id_product = ' . (int)$id_product . '
            AND sm.date_add >= "' . pSQL($date_from) . '"';

        if ($id_product_attribute) {
            $sql .= ' AND sm.id_product_attribute = ' . (int)$id_product_attribute;
        }

        $sql .= ' GROUP BY DATE(sm.date_add) ORDER BY movement_date ASC';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Calculate performance metrics
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $period_days
     * @return array
     */
    protected function calculatePerformanceMetrics($id_product, $id_product_attribute, $period_days)
    {
        // Current stock
        if ($id_product_attribute) {
            $current_stock = Db::getInstance()->getValue('
                SELECT quantity FROM ' . _DB_PREFIX_ . 'product_attribute 
                WHERE id_product_attribute = ' . (int)$id_product_attribute
            );
        } else {
            $current_stock = Db::getInstance()->getValue('
                SELECT quantity FROM ' . _DB_PREFIX_ . 'product 
                WHERE id_product = ' . (int)$id_product
            );
        }

        // Sales velocity (units per day)
        $total_sold = $this->getTotalSoldQuantity($id_product, $id_product_attribute, $period_days);
        $sales_velocity = $period_days > 0 ? $total_sold / $period_days : 0;

        // Days of inventory remaining
        $days_of_inventory = $sales_velocity > 0 ? $current_stock / $sales_velocity : 999;

        // Conversion rate (if we have view data)
        $conversion_rate = $this->calculateConversionRate($id_product, $id_product_attribute, $period_days);

        // Trend analysis
        $trend = $this->calculateSalesTrend($id_product, $id_product_attribute, $period_days);

        return array(
            'current_stock' => (int)$current_stock,
            'sales_velocity' => round($sales_velocity, 2),
            'days_of_inventory' => $days_of_inventory > 999 ? null : round($days_of_inventory, 1),
            'conversion_rate' => $conversion_rate,
            'trend' => $trend,
            'stock_status' => $this->getStockStatus($current_stock, $sales_velocity),
            'reorder_point' => $this->calculateReorderPoint($sales_velocity)
        );
    }

    /**
     * Get total sold quantity for period
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $period_days
     * @return int
     */
    protected function getTotalSoldQuantity($id_product, $id_product_attribute, $period_days)
    {
        $date_from = date('Y-m-d H:i:s', strtotime('-' . $period_days . ' days'));
        
        $sql = '
            SELECT SUM(od.product_quantity) as total_quantity
            FROM ' . _DB_PREFIX_ . 'order_detail od
            LEFT JOIN ' . _DB_PREFIX_ . 'orders o ON o.id_order = od.id_order
            WHERE od.product_id = ' . (int)$id_product . '
            AND o.date_add >= "' . pSQL($date_from) . '"
            AND o.valid = 1';

        if ($id_product_attribute) {
            $sql .= ' AND od.product_attribute_id = ' . (int)$id_product_attribute;
        }

        return (int)Db::getInstance()->getValue($sql);
    }

    /**
     * Calculate conversion rate (placeholder - would need page view tracking)
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $period_days
     * @return float|null
     */
    protected function calculateConversionRate($id_product, $id_product_attribute, $period_days)
    {
        // This would require page view tracking implementation
        // For now, return null to indicate data not available
        return null;
    }

    /**
     * Calculate sales trend
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $period_days
     * @return string
     */
    protected function calculateSalesTrend($id_product, $id_product_attribute, $period_days)
    {
        $half_period = floor($period_days / 2);
        
        // Get sales for first half vs second half
        $first_half_sales = $this->getSalesForPeriod($id_product, $id_product_attribute, $period_days, $half_period);
        $second_half_sales = $this->getSalesForPeriod($id_product, $id_product_attribute, $half_period, 0);

        if ($first_half_sales == 0 && $second_half_sales == 0) {
            return 'stable';
        }

        if ($first_half_sales == 0) {
            return 'increasing';
        }

        $change_percent = (($second_half_sales - $first_half_sales) / $first_half_sales) * 100;

        if ($change_percent > 10) {
            return 'increasing';
        } elseif ($change_percent < -10) {
            return 'decreasing';
        } else {
            return 'stable';
        }
    }

    /**
     * Get sales for specific period
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param int $days_ago_start
     * @param int $days_ago_end
     * @return int
     */
    protected function getSalesForPeriod($id_product, $id_product_attribute, $days_ago_start, $days_ago_end)
    {
        $date_from = date('Y-m-d H:i:s', strtotime('-' . $days_ago_start . ' days'));
        $date_to = date('Y-m-d H:i:s', strtotime('-' . $days_ago_end . ' days'));
        
        $sql = '
            SELECT SUM(od.product_quantity) as total_quantity
            FROM ' . _DB_PREFIX_ . 'order_detail od
            LEFT JOIN ' . _DB_PREFIX_ . 'orders o ON o.id_order = od.id_order
            WHERE od.product_id = ' . (int)$id_product . '
            AND o.date_add >= "' . pSQL($date_from) . '"
            AND o.date_add <= "' . pSQL($date_to) . '"
            AND o.valid = 1';

        if ($id_product_attribute) {
            $sql .= ' AND od.product_attribute_id = ' . (int)$id_product_attribute;
        }

        return (int)Db::getInstance()->getValue($sql);
    }

    /**
     * Get stock status based on current stock and sales velocity
     *
     * @param int $current_stock
     * @param float $sales_velocity
     * @return string
     */
    protected function getStockStatus($current_stock, $sales_velocity)
    {
        if ($current_stock <= 0) {
            return 'out_of_stock';
        }

        if ($sales_velocity <= 0) {
            return 'overstocked';
        }

        $days_remaining = $current_stock / $sales_velocity;

        if ($days_remaining < 7) {
            return 'critical';
        } elseif ($days_remaining < 14) {
            return 'low';
        } elseif ($days_remaining < 30) {
            return 'normal';
        } else {
            return 'high';
        }
    }

    /**
     * Calculate reorder point
     *
     * @param float $sales_velocity
     * @return int
     */
    protected function calculateReorderPoint($sales_velocity)
    {
        // Simple reorder point calculation: 14 days of sales + safety stock (7 days)
        $lead_time_days = 14;
        $safety_stock_days = 7;
        
        return ceil($sales_velocity * ($lead_time_days + $safety_stock_days));
    }

    /**
     * Generate analytics report for admin
     *
     * @param int $period_days
     * @return array
     */
    public function generateAdminReport($period_days = 30)
    {
        // Top performing equivalent product groups
        $top_groups = $this->getTopPerformingGroups($period_days);
        
        // Overall module performance
        $module_performance = $this->getModulePerformance($period_days);
        
        // Stock alerts
        $stock_alerts = $this->getStockAlerts();
        
        return array(
            'top_groups' => $top_groups,
            'module_performance' => $module_performance,
            'stock_alerts' => $stock_alerts,
            'period_days' => $period_days,
            'generated_at' => date('Y-m-d H:i:s')
        );
    }

    /**
     * Get top performing equivalent product groups
     *
     * @param int $period_days
     * @return array
     */
    protected function getTopPerformingGroups($period_days)
    {
        $date_from = date('Y-m-d H:i:s', strtotime('-' . $period_days . ' days'));
        
        $sql = '
            SELECT 
                g.group_hash,
                g.attributes_hash,
                COUNT(DISTINCT pgp.id_product) as product_count,
                SUM(od.product_quantity) as total_quantity_sold,
                SUM(od.total_price_tax_incl) as total_revenue
            FROM ' . _DB_PREFIX_ . 'stequivalent_product_groups g
            LEFT JOIN ' . _DB_PREFIX_ . 'stequivalent_product_group_products pgp ON pgp.id_group = g.id_group
            LEFT JOIN ' . _DB_PREFIX_ . 'order_detail od ON od.product_id = pgp.id_product
            LEFT JOIN ' . _DB_PREFIX_ . 'orders o ON o.id_order = od.id_order
            WHERE o.date_add >= "' . pSQL($date_from) . '"
            AND o.valid = 1
            GROUP BY g.id_group
            HAVING total_quantity_sold > 0
            ORDER BY total_revenue DESC
            LIMIT 10';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get overall module performance metrics
     *
     * @param int $period_days
     * @return array
     */
    protected function getModulePerformance($period_days)
    {
        $date_from = date('Y-m-d H:i:s', strtotime('-' . $period_days . ' days'));
        
        // Total sales through equivalent products
        $total_sales = Db::getInstance()->getRow('
            SELECT 
                COUNT(DISTINCT od.id_order) as order_count,
                SUM(od.product_quantity) as quantity_sold,
                SUM(od.total_price_tax_incl) as revenue
            FROM ' . _DB_PREFIX_ . 'order_detail od
            LEFT JOIN ' . _DB_PREFIX_ . 'orders o ON o.id_order = od.id_order
            LEFT JOIN ' . _DB_PREFIX_ . 'stequivalent_product_group_products pgp ON pgp.id_product = od.product_id
            WHERE o.date_add >= "' . pSQL($date_from) . '"
            AND o.valid = 1
            AND pgp.id_product IS NOT NULL'
        );

        return array(
            'total_orders' => (int)($total_sales['order_count'] ?: 0),
            'total_quantity' => (int)($total_sales['quantity_sold'] ?: 0),
            'total_revenue' => (float)($total_sales['revenue'] ?: 0),
            'avg_order_value' => $total_sales['order_count'] > 0 ? 
                $total_sales['revenue'] / $total_sales['order_count'] : 0
        );
    }

    /**
     * Get stock alerts for products in equivalent groups
     *
     * @return array
     */
    protected function getStockAlerts()
    {
        $sql = '
            SELECT DISTINCT
                p.id_product,
                pl.name as product_name,
                p.reference,
                COALESCE(pa.quantity, p.quantity) as current_stock,
                pa.id_product_attribute
            FROM ' . _DB_PREFIX_ . 'stequivalent_product_group_products pgp
            LEFT JOIN ' . _DB_PREFIX_ . 'product p ON p.id_product = pgp.id_product
            LEFT JOIN ' . _DB_PREFIX_ . 'product_lang pl ON pl.id_product = p.id_product
            LEFT JOIN ' . _DB_PREFIX_ . 'product_attribute pa ON pa.id_product_attribute = pgp.id_product_attribute
            WHERE pl.id_lang = ' . (int)Context::getContext()->language->id . '
            AND p.active = 1
            AND COALESCE(pa.quantity, p.quantity) <= 5
            ORDER BY current_stock ASC
            LIMIT 20';

        return Db::getInstance()->executeS($sql);
    }
}
