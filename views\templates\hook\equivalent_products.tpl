{*
* Front office template for displaying equivalent products
*
* <AUTHOR>
* @version 1.0.0
*}

{if $equivalent_products && count($equivalent_products) > 0}
<div id="stequivalent-products-block" class="stequivalent-block">
    <h3 class="stequivalent-title">
        <i class="icon-th-large"></i>
        {l s='Similar Products' mod='stequivalentproducttype'}
        <span class="stequivalent-count">({count($equivalent_products)})</span>
    </h3>
    
    <div class="stequivalent-description">
        {l s='Discover other products with similar specifications:' mod='stequivalentproducttype'}
    </div>

    {if $display_mode == 'table'}
        {* Table Display Mode *}
        <div class="stequivalent-table-container">
            <table class="table table-striped stequivalent-table">
                <thead>
                    <tr>
                        {if $show_images}
                            <th class="stequivalent-th-image">{l s='Image' mod='stequivalentproducttype'}</th>
                        {/if}
                        <th class="stequivalent-th-name">{l s='Product' mod='stequivalentproducttype'}</th>
                        <th class="stequivalent-th-attributes">{l s='Specifications' mod='stequivalentproducttype'}</th>
                        {if $show_price}
                            <th class="stequivalent-th-price">{l s='Price' mod='stequivalentproducttype'}</th>
                        {/if}
                        {if $show_stock}
                            <th class="stequivalent-th-stock">{l s='Stock' mod='stequivalentproducttype'}</th>
                        {/if}
                        {if $show_sales}
                            <th class="stequivalent-th-sales">{l s='Sales' mod='stequivalentproducttype'}</th>
                        {/if}
                        <th class="stequivalent-th-action">{l s='Action' mod='stequivalentproducttype'}</th>
                    </tr>
                </thead>
                <tbody>
                    {foreach from=$equivalent_products item=product}
                        <tr class="stequivalent-product-row">
                            {if $show_images}
                                <td class="stequivalent-td-image">
                                    {if $product.images && count($product.images) > 0}
                                        <img src="{$link->getImageLink($product.reference, $product.images[0].id_image, 'small_default')}" 
                                             alt="{$product.name|escape:'html':'UTF-8'}" 
                                             class="stequivalent-product-image" />
                                    {else}
                                        <div class="stequivalent-no-image">
                                            <i class="icon-picture-o"></i>
                                        </div>
                                    {/if}
                                </td>
                            {/if}
                            <td class="stequivalent-td-name">
                                <a href="{$link->getProductLink($product.id_product, null, null, null, null, null, $product.id_product_attribute)}" 
                                   class="stequivalent-product-link">
                                    {$product.name|escape:'html':'UTF-8'}
                                </a>
                                {if $product.reference}
                                    <br><small class="stequivalent-reference">Ref: {$product.reference|escape:'html':'UTF-8'}</small>
                                {/if}
                            </td>
                            <td class="stequivalent-td-attributes">
                                {if $product.attributes && count($product.attributes) > 0}
                                    <div class="stequivalent-attributes">
                                        {foreach from=$product.attributes item=attr}
                                            <span class="stequivalent-attribute">
                                                <strong>{$attr.group_name|escape:'html':'UTF-8'}:</strong> 
                                                {$attr.attribute_name|escape:'html':'UTF-8'}
                                            </span>
                                        {/foreach}
                                    </div>
                                {else}
                                    <span class="stequivalent-no-attributes">-</span>
                                {/if}
                            </td>
                            {if $show_price}
                                <td class="stequivalent-td-price">
                                    <span class="stequivalent-price">
                                        {convertPrice price=$product.final_price}
                                    </span>
                                </td>
                            {/if}
                            {if $show_stock}
                                <td class="stequivalent-td-stock">
                                    {if $product.stock_info.in_stock}
                                        <span class="stequivalent-stock in-stock">
                                            <i class="icon-check-circle"></i>
                                            {l s='In Stock' mod='stequivalentproducttype'} ({$product.stock_info.quantity})
                                        </span>
                                    {else}
                                        <span class="stequivalent-stock out-of-stock">
                                            <i class="icon-times-circle"></i>
                                            {l s='Out of Stock' mod='stequivalentproducttype'}
                                        </span>
                                    {/if}
                                </td>
                            {/if}
                            {if $show_sales}
                                <td class="stequivalent-td-sales">
                                    {if $product.sales_info}
                                        <div class="stequivalent-sales">
                                            <div class="sales-quantity">
                                                <strong>{$product.sales_info.total_quantity|intval}</strong> 
                                                {l s='sold' mod='stequivalentproducttype'}
                                            </div>
                                            <small class="sales-period">
                                                ({l s='Last %d days' sprintf=[$product.sales_info.period_days] mod='stequivalentproducttype'})
                                            </small>
                                        </div>
                                    {else}
                                        <span class="stequivalent-no-sales">-</span>
                                    {/if}
                                </td>
                            {/if}
                            <td class="stequivalent-td-action">
                                <a href="{$link->getProductLink($product.id_product, null, null, null, null, null, $product.id_product_attribute)}" 
                                   class="btn btn-primary btn-sm stequivalent-view-btn">
                                    <i class="icon-eye"></i>
                                    {l s='View' mod='stequivalentproducttype'}
                                </a>
                            </td>
                        </tr>
                    {/foreach}
                </tbody>
            </table>
        </div>

    {elseif $display_mode == 'list'}
        {* List Display Mode *}
        <div class="stequivalent-list-container">
            {foreach from=$equivalent_products item=product}
                <div class="stequivalent-product-item list-item">
                    <div class="stequivalent-item-content">
                        {if $show_images}
                            <div class="stequivalent-item-image">
                                {if $product.images && count($product.images) > 0}
                                    <img src="{$link->getImageLink($product.reference, $product.images[0].id_image, 'medium_default')}" 
                                         alt="{$product.name|escape:'html':'UTF-8'}" 
                                         class="stequivalent-product-image" />
                                {else}
                                    <div class="stequivalent-no-image">
                                        <i class="icon-picture-o"></i>
                                    </div>
                                {/if}
                            </div>
                        {/if}
                        
                        <div class="stequivalent-item-details">
                            <h4 class="stequivalent-item-name">
                                <a href="{$link->getProductLink($product.id_product, null, null, null, null, null, $product.id_product_attribute)}" 
                                   class="stequivalent-product-link">
                                    {$product.name|escape:'html':'UTF-8'}
                                </a>
                            </h4>
                            
                            {if $product.reference}
                                <div class="stequivalent-item-reference">
                                    <strong>{l s='Reference:' mod='stequivalentproducttype'}</strong> {$product.reference|escape:'html':'UTF-8'}
                                </div>
                            {/if}
                            
                            {if $product.attributes && count($product.attributes) > 0}
                                <div class="stequivalent-item-attributes">
                                    {foreach from=$product.attributes item=attr}
                                        <span class="stequivalent-attribute-badge">
                                            {$attr.group_name|escape:'html':'UTF-8'}: {$attr.attribute_name|escape:'html':'UTF-8'}
                                        </span>
                                    {/foreach}
                                </div>
                            {/if}
                            
                            <div class="stequivalent-item-info">
                                {if $show_price}
                                    <div class="stequivalent-item-price">
                                        <strong>{convertPrice price=$product.final_price}</strong>
                                    </div>
                                {/if}
                                
                                {if $show_stock}
                                    <div class="stequivalent-item-stock">
                                        {if $product.stock_info.in_stock}
                                            <span class="stock-status in-stock">
                                                <i class="icon-check"></i> {l s='In Stock' mod='stequivalentproducttype'} ({$product.stock_info.quantity})
                                            </span>
                                        {else}
                                            <span class="stock-status out-of-stock">
                                                <i class="icon-times"></i> {l s='Out of Stock' mod='stequivalentproducttype'}
                                            </span>
                                        {/if}
                                    </div>
                                {/if}
                                
                                {if $show_sales && $product.sales_info}
                                    <div class="stequivalent-item-sales">
                                        <i class="icon-shopping-cart"></i>
                                        {$product.sales_info.total_quantity|intval} {l s='sold in last %d days' sprintf=[$product.sales_info.period_days] mod='stequivalentproducttype'}
                                    </div>
                                {/if}
                            </div>
                            
                            <div class="stequivalent-item-actions">
                                <a href="{$link->getProductLink($product.id_product, null, null, null, null, null, $product.id_product_attribute)}" 
                                   class="btn btn-primary stequivalent-view-btn">
                                    {l s='View Product' mod='stequivalentproducttype'}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {/foreach}
        </div>

    {else}
        {* Grid Display Mode (Default) *}
        <div class="stequivalent-grid-container">
            <div class="row stequivalent-products-grid">
                {foreach from=$equivalent_products item=product}
                    <div class="col-xs-6 col-sm-4 col-md-3 stequivalent-product-col">
                        <div class="stequivalent-product-item grid-item">
                            {if $show_images}
                                <div class="stequivalent-item-image">
                                    {if $product.images && count($product.images) > 0}
                                        <a href="{$link->getProductLink($product.id_product, null, null, null, null, null, $product.id_product_attribute)}">
                                            <img src="{$link->getImageLink($product.reference, $product.images[0].id_image, 'home_default')}" 
                                                 alt="{$product.name|escape:'html':'UTF-8'}" 
                                                 class="stequivalent-product-image" />
                                        </a>
                                    {else}
                                        <div class="stequivalent-no-image">
                                            <i class="icon-picture-o"></i>
                                        </div>
                                    {/if}
                                </div>
                            {/if}
                            
                            <div class="stequivalent-item-content">
                                <h5 class="stequivalent-item-name">
                                    <a href="{$link->getProductLink($product.id_product, null, null, null, null, null, $product.id_product_attribute)}" 
                                       class="stequivalent-product-link">
                                        {$product.name|truncate:50|escape:'html':'UTF-8'}
                                    </a>
                                </h5>
                                
                                {if $product.attributes && count($product.attributes) > 0}
                                    <div class="stequivalent-item-attributes">
                                        {foreach from=$product.attributes item=attr name=attrs}
                                            <small class="stequivalent-attribute">
                                                {$attr.attribute_name|escape:'html':'UTF-8'}{if !$smarty.foreach.attrs.last}, {/if}
                                            </small>
                                        {/foreach}
                                    </div>
                                {/if}
                                
                                {if $show_price}
                                    <div class="stequivalent-item-price">
                                        <strong>{convertPrice price=$product.final_price}</strong>
                                    </div>
                                {/if}
                                
                                {if $show_stock}
                                    <div class="stequivalent-item-stock">
                                        {if $product.stock_info.in_stock}
                                            <span class="stock-badge in-stock">
                                                <i class="icon-check"></i> {l s='Available' mod='stequivalentproducttype'}
                                            </span>
                                        {else}
                                            <span class="stock-badge out-of-stock">
                                                <i class="icon-times"></i> {l s='Out of Stock' mod='stequivalentproducttype'}
                                            </span>
                                        {/if}
                                    </div>
                                {/if}
                                
                                {if $show_sales && $product.sales_info}
                                    <div class="stequivalent-item-sales">
                                        <small>
                                            <i class="icon-shopping-cart"></i>
                                            {$product.sales_info.total_quantity|intval} {l s='sold' mod='stequivalentproducttype'}
                                        </small>
                                    </div>
                                {/if}
                                
                                <div class="stequivalent-item-actions">
                                    <a href="{$link->getProductLink($product.id_product, null, null, null, null, null, $product.id_product_attribute)}" 
                                       class="btn btn-primary btn-sm btn-block stequivalent-view-btn">
                                        {l s='View' mod='stequivalentproducttype'}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {/foreach}
            </div>
        </div>
    {/if}
</div>
{/if}
