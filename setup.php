<?php
/**
 * Setup script for Equivalent Product Types Module
 * This script helps install Composer dependencies if needed
 *
 * <AUTHOR>
 * @version 1.0.0
 */

// Check if we're in the correct directory
if (!file_exists('stequivalentproducttype.php')) {
    die("Error: Please run this script from the module directory.\n");
}

echo "=== Equivalent Product Types Module Setup ===\n\n";

// Check PHP version
$phpVersion = PHP_VERSION;
echo "PHP Version: $phpVersion\n";

if (version_compare($phpVersion, '8.1.0', '<')) {
    echo "⚠️  Warning: PHP 8.1+ is recommended for optimal performance.\n";
}

// Check if Composer is available
$composerAvailable = false;
$composerPath = null;

// Check for composer in common locations
$composerPaths = [
    'composer',
    'composer.phar',
    '/usr/local/bin/composer',
    '/usr/bin/composer'
];

foreach ($composerPaths as $path) {
    $output = [];
    $returnCode = 0;
    exec("$path --version 2>/dev/null", $output, $returnCode);
    
    if ($returnCode === 0) {
        $composerAvailable = true;
        $composerPath = $path;
        echo "✅ Composer found: $path\n";
        break;
    }
}

if (!$composerAvailable) {
    echo "⚠️  Composer not found in PATH\n";
    echo "   The module will work without Composer using manual autoloading.\n";
    echo "   For optimal performance, consider installing Composer.\n\n";
} else {
    echo "\n";
}

// Check if vendor directory exists
if (file_exists('vendor/autoload.php')) {
    echo "✅ Composer dependencies already installed\n";
} else {
    if ($composerAvailable) {
        echo "📦 Installing Composer dependencies...\n";
        
        $output = [];
        $returnCode = 0;
        exec("$composerPath install --no-dev --optimize-autoloader 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "✅ Composer dependencies installed successfully\n";
        } else {
            echo "❌ Failed to install Composer dependencies\n";
            echo "   Output: " . implode("\n   ", $output) . "\n";
            echo "   The module will use manual autoloading instead.\n";
        }
    } else {
        echo "ℹ️  Composer dependencies not installed (Composer not available)\n";
        echo "   The module will use manual autoloading.\n";
    }
}

echo "\n";

// Check file permissions
$checkPaths = [
    'translations/',
    'views/css/',
    'views/js/',
    'views/templates/'
];

echo "🔒 Checking file permissions...\n";
$permissionIssues = false;

foreach ($checkPaths as $path) {
    if (file_exists($path)) {
        if (is_writable($path)) {
            echo "✅ $path is writable\n";
        } else {
            echo "⚠️  $path is not writable\n";
            $permissionIssues = true;
        }
    } else {
        echo "⚠️  $path does not exist\n";
    }
}

if ($permissionIssues) {
    echo "\n💡 To fix permission issues, run:\n";
    echo "   chmod -R 755 " . dirname(__FILE__) . "\n";
    echo "   chmod -R 777 " . dirname(__FILE__) . "/translations/\n\n";
}

// Check PrestaShop compatibility
echo "🔍 Checking PrestaShop compatibility...\n";

// Try to detect PrestaShop
$prestashopPaths = [
    '../../config/config.inc.php',
    '../../../config/config.inc.php',
    '../../../../config/config.inc.php'
];

$prestashopFound = false;
foreach ($prestashopPaths as $configPath) {
    if (file_exists($configPath)) {
        $prestashopFound = true;
        echo "✅ PrestaShop installation detected\n";
        
        // Try to get PrestaShop version
        $configContent = file_get_contents($configPath);
        if (preg_match('/define\([\'"]_PS_VERSION_[\'"],\s*[\'"]([^\'\"]+)[\'"]/', $configContent, $matches)) {
            $psVersion = $matches[1];
            echo "   PrestaShop Version: $psVersion\n";
            
            if (version_compare($psVersion, '1.7.0', '>=')) {
                echo "✅ PrestaShop version is compatible\n";
            } else {
                echo "⚠️  PrestaShop version may not be fully compatible (1.7+ recommended)\n";
            }
        }
        break;
    }
}

if (!$prestashopFound) {
    echo "⚠️  PrestaShop installation not detected\n";
    echo "   Make sure this module is in the correct modules directory\n";
}

echo "\n";

// Check database requirements
echo "🗄️  Database requirements:\n";
echo "   - MySQL 5.7+ or MariaDB 10.2+\n";
echo "   - InnoDB storage engine support\n";
echo "   - UTF-8 character set support\n";

echo "\n";

// Module status summary
echo "📋 Setup Summary:\n";
echo "   Module: Equivalent Product Types v1.0.0\n";
echo "   Author: Sathi\n";
echo "   Autoloading: " . (file_exists('vendor/autoload.php') ? 'Composer' : 'Manual') . "\n";
echo "   Status: Ready for installation\n";

echo "\n";

// Installation instructions
echo "🚀 Next Steps:\n";
echo "1. Log into your PrestaShop admin panel\n";
echo "2. Go to Modules → Module Manager\n";
echo "3. Find 'Equivalent Product Types' and click 'Install'\n";
echo "4. After installation, click 'Configure' to set up the module\n";
echo "5. Configure attributes and display options as needed\n";

echo "\n";

// Optional: Run basic tests
if (isset($argv[1]) && $argv[1] === '--test') {
    echo "🧪 Running basic tests...\n";
    
    // Test autoloading
    try {
        if (file_exists('vendor/autoload.php')) {
            require_once 'vendor/autoload.php';
        }
        
        // Test service classes
        if (file_exists('src/Service/EquivalentProductService.php')) {
            require_once 'src/Service/EquivalentProductService.php';
            echo "✅ EquivalentProductService can be loaded\n";
        }
        
        if (file_exists('src/Service/ProductGroupService.php')) {
            require_once 'src/Service/ProductGroupService.php';
            echo "✅ ProductGroupService can be loaded\n";
        }
        
        if (file_exists('src/Service/AnalyticsService.php')) {
            require_once 'src/Service/AnalyticsService.php';
            echo "✅ AnalyticsService can be loaded\n";
        }
        
        echo "✅ All service classes loaded successfully\n";
        
    } catch (Exception $e) {
        echo "❌ Error loading classes: " . $e->getMessage() . "\n";
    }
}

echo "\n=== Setup Complete ===\n";

// If running from command line, provide additional info
if (php_sapi_name() === 'cli') {
    echo "\nTo run this setup with tests: php setup.php --test\n";
    echo "For more information, see README.md and INSTALLATION.md\n";
}
?>
