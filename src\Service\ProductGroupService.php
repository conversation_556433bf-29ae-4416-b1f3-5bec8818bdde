<?php
/**
 * Service for managing equivalent product groups
 *
 * <AUTHOR>
 * @version 1.0.0
 */

namespace StEquivalentProductType\Service;

use Db;
use Configuration;

class ProductGroupService
{
    /**
     * Create or update product group based on attributes
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @param array $attributes
     * @return int|false Group ID or false on error
     */
    public function createOrUpdateProductGroup($id_product, $id_product_attribute, $attributes)
    {
        if (empty($attributes)) {
            return false;
        }

        // Generate hash for attributes
        $attributes_hash = $this->generateAttributesHash($attributes);
        $group_hash = md5($attributes_hash);

        // Check if group already exists
        $id_group = Db::getInstance()->getValue('
            SELECT id_group 
            FROM ' . _DB_PREFIX_ . 'stequivalent_product_groups 
            WHERE group_hash = "' . pSQL($group_hash) . '"
        ');

        if (!$id_group) {
            // Create new group
            $group_data = array(
                'group_hash' => $group_hash,
                'attributes_hash' => $attributes_hash,
                'date_add' => date('Y-m-d H:i:s'),
                'date_upd' => date('Y-m-d H:i:s')
            );

            if (Db::getInstance()->insert('stequivalent_product_groups', $group_data)) {
                $id_group = Db::getInstance()->Insert_ID();
            } else {
                return false;
            }
        }

        // Add product to group
        $this->addProductToGroup($id_group, $id_product, $id_product_attribute);

        return $id_group;
    }

    /**
     * Add product to existing group
     *
     * @param int $id_group
     * @param int $id_product
     * @param int $id_product_attribute
     * @return bool
     */
    public function addProductToGroup($id_group, $id_product, $id_product_attribute = null)
    {
        // Check if product is already in group
        $exists = Db::getInstance()->getValue('
            SELECT COUNT(*) 
            FROM ' . _DB_PREFIX_ . 'stequivalent_product_group_products 
            WHERE id_group = ' . (int)$id_group . ' 
            AND id_product = ' . (int)$id_product
        );

        if ($exists) {
            return true; // Already exists
        }

        $data = array(
            'id_group' => (int)$id_group,
            'id_product' => (int)$id_product,
            'id_product_attribute' => $id_product_attribute ? (int)$id_product_attribute : null,
            'date_add' => date('Y-m-d H:i:s')
        );

        return Db::getInstance()->insert('stequivalent_product_group_products', $data);
    }

    /**
     * Remove product from group
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @return bool
     */
    public function removeProductFromGroup($id_product, $id_product_attribute = null)
    {
        $where = 'id_product = ' . (int)$id_product;
        if ($id_product_attribute) {
            $where .= ' AND id_product_attribute = ' . (int)$id_product_attribute;
        }

        return Db::getInstance()->delete('stequivalent_product_group_products', $where);
    }

    /**
     * Get products in the same group as given product
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @return array
     */
    public function getProductsInSameGroup($id_product, $id_product_attribute = null)
    {
        // First, find the group(s) this product belongs to
        $where = 'pgp.id_product = ' . (int)$id_product;
        if ($id_product_attribute) {
            $where .= ' AND pgp.id_product_attribute = ' . (int)$id_product_attribute;
        }

        $groups = Db::getInstance()->executeS('
            SELECT DISTINCT pgp.id_group
            FROM ' . _DB_PREFIX_ . 'stequivalent_product_group_products pgp
            WHERE ' . $where
        );

        if (empty($groups)) {
            return array();
        }

        $group_ids = array_column($groups, 'id_group');
        $group_ids_str = implode(',', array_map('intval', $group_ids));

        // Get all products in these groups (excluding the original product)
        $sql = '
            SELECT DISTINCT pgp.id_product, pgp.id_product_attribute, p.reference, pl.name
            FROM ' . _DB_PREFIX_ . 'stequivalent_product_group_products pgp
            LEFT JOIN ' . _DB_PREFIX_ . 'product p ON p.id_product = pgp.id_product
            LEFT JOIN ' . _DB_PREFIX_ . 'product_lang pl ON pl.id_product = p.id_product
            WHERE pgp.id_group IN (' . $group_ids_str . ')
            AND pgp.id_product != ' . (int)$id_product . '
            AND pl.id_lang = ' . (int)\Context::getContext()->language->id . '
            AND p.active = 1
            ORDER BY pl.name ASC';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Generate hash for attributes array
     *
     * @param array $attributes
     * @return string
     */
    protected function generateAttributesHash($attributes)
    {
        // Sort attributes by key to ensure consistent hashing
        ksort($attributes);
        
        $hash_parts = array();
        foreach ($attributes as $key => $value) {
            $hash_parts[] = $key . ':' . $value;
        }

        return implode('|', $hash_parts);
    }

    /**
     * Rebuild all product groups
     * This method should be called when attribute mappings change
     *
     * @return bool
     */
    public function rebuildAllGroups()
    {
        // Clear existing groups
        Db::getInstance()->execute('TRUNCATE TABLE ' . _DB_PREFIX_ . 'stequivalent_product_groups');
        Db::getInstance()->execute('TRUNCATE TABLE ' . _DB_PREFIX_ . 'stequivalent_product_group_products');

        // Get all products with attributes
        $sql = '
            SELECT DISTINCT p.id_product, pa.id_product_attribute
            FROM ' . _DB_PREFIX_ . 'product p
            LEFT JOIN ' . _DB_PREFIX_ . 'product_attribute pa ON pa.id_product = p.id_product
            WHERE p.active = 1
            ORDER BY p.id_product ASC';

        $products = Db::getInstance()->executeS($sql);

        if (empty($products)) {
            return true;
        }

        require_once dirname(__FILE__) . '/EquivalentProductService.php';
        $equivalentService = new EquivalentProductService();

        $processed = 0;
        foreach ($products as $product) {
            // Get enabled attributes for matching
            $enabled_attributes = json_decode(Configuration::get('STEQUIVALENT_ATTRIBUTES'), true);
            if (!$enabled_attributes || empty($enabled_attributes)) {
                continue;
            }

            // Get product attributes
            $attributes = $equivalentService->getProductAttributes(
                $product['id_product'], 
                $product['id_product_attribute'], 
                $enabled_attributes
            );

            if (!empty($attributes)) {
                $this->createOrUpdateProductGroup(
                    $product['id_product'],
                    $product['id_product_attribute'],
                    $attributes
                );
                $processed++;
            }
        }

        return $processed > 0;
    }

    /**
     * Get group statistics
     *
     * @return array
     */
    public function getGroupStatistics()
    {
        $stats = array();

        // Total groups
        $stats['total_groups'] = Db::getInstance()->getValue('
            SELECT COUNT(*) FROM ' . _DB_PREFIX_ . 'stequivalent_product_groups'
        );

        // Average products per group
        $stats['avg_products_per_group'] = Db::getInstance()->getValue('
            SELECT AVG(product_count) 
            FROM (
                SELECT COUNT(id_product) as product_count
                FROM ' . _DB_PREFIX_ . 'stequivalent_product_group_products
                GROUP BY id_group
            ) as group_counts'
        );

        // Largest group
        $stats['largest_group'] = Db::getInstance()->getRow('
            SELECT id_group, COUNT(id_product) as product_count
            FROM ' . _DB_PREFIX_ . 'stequivalent_product_group_products
            GROUP BY id_group
            ORDER BY product_count DESC
            LIMIT 1'
        );

        // Groups by attribute hash (most common attribute combinations)
        $stats['common_attributes'] = Db::getInstance()->executeS('
            SELECT attributes_hash, COUNT(*) as group_count
            FROM ' . _DB_PREFIX_ . 'stequivalent_product_groups
            GROUP BY attributes_hash
            ORDER BY group_count DESC
            LIMIT 10'
        );

        return $stats;
    }

    /**
     * Clean up empty groups
     *
     * @return int Number of groups removed
     */
    public function cleanupEmptyGroups()
    {
        // Find groups with no products
        $empty_groups = Db::getInstance()->executeS('
            SELECT g.id_group
            FROM ' . _DB_PREFIX_ . 'stequivalent_product_groups g
            LEFT JOIN ' . _DB_PREFIX_ . 'stequivalent_product_group_products pgp ON pgp.id_group = g.id_group
            WHERE pgp.id_group IS NULL'
        );

        if (empty($empty_groups)) {
            return 0;
        }

        $group_ids = array_column($empty_groups, 'id_group');
        $group_ids_str = implode(',', array_map('intval', $group_ids));

        // Delete empty groups
        Db::getInstance()->execute('
            DELETE FROM ' . _DB_PREFIX_ . 'stequivalent_product_groups 
            WHERE id_group IN (' . $group_ids_str . ')'
        );

        return count($empty_groups);
    }

    /**
     * Update group when product attributes change
     *
     * @param int $id_product
     * @param int $id_product_attribute
     * @return bool
     */
    public function updateProductGroup($id_product, $id_product_attribute = null)
    {
        // Remove from existing groups
        $this->removeProductFromGroup($id_product, $id_product_attribute);

        // Get current attributes
        $enabled_attributes = json_decode(Configuration::get('STEQUIVALENT_ATTRIBUTES'), true);
        if (!$enabled_attributes || empty($enabled_attributes)) {
            return true; // No attributes to match
        }

        require_once dirname(__FILE__) . '/EquivalentProductService.php';
        $equivalentService = new EquivalentProductService();

        $attributes = $equivalentService->getProductAttributes(
            $id_product, 
            $id_product_attribute, 
            $enabled_attributes
        );

        if (!empty($attributes)) {
            return $this->createOrUpdateProductGroup($id_product, $id_product_attribute, $attributes) !== false;
        }

        return true;
    }
}
