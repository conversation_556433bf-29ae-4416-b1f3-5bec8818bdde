<?php
/**
 * Uninstallation SQL for Equivalent Product Types Module
 *
 * <AUTHOR>
 * @version 1.0.0
 */

$sql = array();

// Drop all module tables
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stequivalent_product_groups`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stequivalent_product_group_products`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stequivalent_sales_cache`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stequivalent_configuration`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stequivalent_attribute_mapping`';

foreach ($sql as $query) {
    if (Db::getInstance()->execute($query) == false) {
        return false;
    }
}
