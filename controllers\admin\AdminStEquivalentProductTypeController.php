<?php
/**
 * Admin Controller for Equivalent Product Types Module
 *
 * <AUTHOR>
 * @version 1.0.0
 */

class AdminStEquivalentProductTypeController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->table = 'stequivalent_attribute_mapping';
        $this->className = 'StEquivalentAttributeMapping';
        $this->lang = false;
        $this->addRowAction('edit');
        $this->addRowAction('delete');
        $this->bulk_actions = array(
            'delete' => array(
                'text' => $this->l('Delete selected'),
                'confirm' => $this->l('Delete selected items?')
            )
        );

        $this->fields_list = array(
            'id_mapping' => array(
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ),
            'attribute_name' => array(
                'title' => $this->l('Attribute Name'),
                'width' => 'auto'
            ),
            'id_attribute_group' => array(
                'title' => $this->l('Attribute Group ID'),
                'align' => 'center',
                'class' => 'fixed-width-sm'
            ),
            'weight' => array(
                'title' => $this->l('Weight'),
                'align' => 'center',
                'class' => 'fixed-width-sm'
            ),
            'is_enabled' => array(
                'title' => $this->l('Enabled'),
                'align' => 'center',
                'active' => 'status',
                'type' => 'bool',
                'class' => 'fixed-width-sm'
            ),
            'date_add' => array(
                'title' => $this->l('Date Added'),
                'type' => 'datetime',
                'align' => 'center',
                'class' => 'fixed-width-lg'
            )
        );

        parent::__construct();
    }

    public function renderForm()
    {
        $this->fields_form = array(
            'legend' => array(
                'title' => $this->l('Attribute Mapping'),
                'icon' => 'icon-cogs'
            ),
            'input' => array(
                array(
                    'type' => 'text',
                    'label' => $this->l('Attribute Name'),
                    'name' => 'attribute_name',
                    'required' => true,
                    'hint' => $this->l('Name of the attribute for display purposes')
                ),
                array(
                    'type' => 'select',
                    'label' => $this->l('Attribute Group'),
                    'name' => 'id_attribute_group',
                    'required' => true,
                    'options' => array(
                        'query' => $this->getAttributeGroups(),
                        'id' => 'id_attribute_group',
                        'name' => 'name'
                    ),
                    'hint' => $this->l('Select the PrestaShop attribute group to map')
                ),
                array(
                    'type' => 'text',
                    'label' => $this->l('Weight'),
                    'name' => 'weight',
                    'required' => true,
                    'class' => 'fixed-width-sm',
                    'hint' => $this->l('Weight for matching priority (higher = more important)')
                ),
                array(
                    'type' => 'switch',
                    'label' => $this->l('Enabled'),
                    'name' => 'is_enabled',
                    'required' => false,
                    'is_bool' => true,
                    'values' => array(
                        array(
                            'id' => 'active_on',
                            'value' => true,
                            'label' => $this->l('Enabled')
                        ),
                        array(
                            'id' => 'active_off',
                            'value' => false,
                            'label' => $this->l('Disabled')
                        )
                    ),
                    'hint' => $this->l('Enable or disable this attribute for equivalent product matching')
                )
            ),
            'submit' => array(
                'title' => $this->l('Save')
            )
        );

        if (!($obj = $this->loadObject(true))) {
            return;
        }

        return parent::renderForm();
    }

    protected function getAttributeGroups()
    {
        $sql = '
            SELECT ag.id_attribute_group, agl.name
            FROM ' . _DB_PREFIX_ . 'attribute_group ag
            LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group_lang agl ON agl.id_attribute_group = ag.id_attribute_group
            WHERE agl.id_lang = ' . (int)$this->context->language->id . '
            ORDER BY agl.name ASC';

        return Db::getInstance()->executeS($sql);
    }

    public function renderList()
    {
        $this->addRowActionSkipList('delete', array());

        return parent::renderList();
    }

    public function postProcess()
    {
        if (Tools::isSubmit('submitAdd' . $this->table)) {
            $id_mapping = (int)Tools::getValue('id_mapping');
            $attribute_name = pSQL(Tools::getValue('attribute_name'));
            $id_attribute_group = (int)Tools::getValue('id_attribute_group');
            $weight = (int)Tools::getValue('weight');
            $is_enabled = (bool)Tools::getValue('is_enabled');

            if (empty($attribute_name) || !$id_attribute_group || !$weight) {
                $this->errors[] = $this->l('Please fill all required fields');
                return false;
            }

            $data = array(
                'attribute_name' => $attribute_name,
                'id_attribute_group' => $id_attribute_group,
                'weight' => $weight,
                'is_enabled' => $is_enabled ? 1 : 0,
                'date_upd' => date('Y-m-d H:i:s')
            );

            if ($id_mapping) {
                // Update existing
                $result = Db::getInstance()->update('stequivalent_attribute_mapping', $data, 'id_mapping = ' . $id_mapping);
            } else {
                // Insert new
                $data['date_add'] = date('Y-m-d H:i:s');
                $result = Db::getInstance()->insert('stequivalent_attribute_mapping', $data);
            }

            if ($result) {
                $this->confirmations[] = $this->l('Attribute mapping saved successfully');
                Tools::redirectAdmin($this->context->link->getAdminLink('AdminStEquivalentProductType'));
            } else {
                $this->errors[] = $this->l('Error saving attribute mapping');
            }
        }

        return parent::postProcess();
    }

    public function renderView()
    {
        $this->toolbar_title = $this->l('Equivalent Product Types - Statistics');
        
        // Get statistics
        $stats = $this->getModuleStatistics();
        
        $this->context->smarty->assign(array(
            'stats' => $stats,
            'module_dir' => $this->module->getPathUri()
        ));

        return $this->context->smarty->fetch($this->module->getLocalPath() . 'views/templates/admin/statistics.tpl');
    }

    protected function getModuleStatistics()
    {
        $stats = array();

        // Total equivalent product groups
        $stats['total_groups'] = Db::getInstance()->getValue('
            SELECT COUNT(*) FROM ' . _DB_PREFIX_ . 'stequivalent_product_groups'
        );

        // Total products with equivalents
        $stats['total_products_with_equivalents'] = Db::getInstance()->getValue('
            SELECT COUNT(DISTINCT id_product) FROM ' . _DB_PREFIX_ . 'stequivalent_product_group_products'
        );

        // Most popular equivalent groups (by product count)
        $stats['popular_groups'] = Db::getInstance()->executeS('
            SELECT g.group_hash, COUNT(pgp.id_product) as product_count
            FROM ' . _DB_PREFIX_ . 'stequivalent_product_groups g
            LEFT JOIN ' . _DB_PREFIX_ . 'stequivalent_product_group_products pgp ON pgp.id_group = g.id_group
            GROUP BY g.id_group
            ORDER BY product_count DESC
            LIMIT 10
        ');

        // Attribute usage statistics
        $stats['attribute_usage'] = Db::getInstance()->executeS('
            SELECT attribute_name, is_enabled, weight
            FROM ' . _DB_PREFIX_ . 'stequivalent_attribute_mapping
            ORDER BY weight DESC, attribute_name ASC
        ');

        // Cache statistics
        $stats['cache_entries'] = Db::getInstance()->getValue('
            SELECT COUNT(*) FROM ' . _DB_PREFIX_ . 'stequivalent_sales_cache'
        );

        $stats['cache_last_updated'] = Db::getInstance()->getValue('
            SELECT MAX(last_updated) FROM ' . _DB_PREFIX_ . 'stequivalent_sales_cache'
        );

        return $stats;
    }

    public function initToolbar()
    {
        parent::initToolbar();

        $this->toolbar_btn['new'] = array(
            'href' => self::$currentIndex . '&add' . $this->table . '&token=' . $this->token,
            'desc' => $this->l('Add new attribute mapping')
        );

        $this->toolbar_btn['stats'] = array(
            'href' => self::$currentIndex . '&view&token=' . $this->token,
            'desc' => $this->l('View Statistics'),
            'icon' => 'process-icon-stats'
        );
    }
}
