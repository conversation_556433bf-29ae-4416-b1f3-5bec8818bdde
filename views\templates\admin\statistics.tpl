{*
* Statistics template for Equivalent Product Types Module
*
* <AUTHOR>
* @version 1.0.0
*}

<div class="panel">
    <div class="panel-heading">
        <i class="icon-bar-chart"></i>
        {l s='Module Statistics' mod='stequivalentproducttype'}
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-lg-3">
                <div class="kpi-container">
                    <div class="kpi kpi-primary">
                        <i class="icon-group"></i>
                        <div class="kpi-content">
                            <span class="kpi-value">{$stats.total_groups|intval}</span>
                            <span class="kpi-title">{l s='Equivalent Groups' mod='stequivalentproducttype'}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3">
                <div class="kpi-container">
                    <div class="kpi kpi-success">
                        <i class="icon-shopping-cart"></i>
                        <div class="kpi-content">
                            <span class="kpi-value">{$stats.total_products_with_equivalents|intval}</span>
                            <span class="kpi-title">{l s='Products with Equivalents' mod='stequivalentproducttype'}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3">
                <div class="kpi-container">
                    <div class="kpi kpi-info">
                        <i class="icon-database"></i>
                        <div class="kpi-content">
                            <span class="kpi-value">{$stats.cache_entries|intval}</span>
                            <span class="kpi-title">{l s='Cache Entries' mod='stequivalentproducttype'}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3">
                <div class="kpi-container">
                    <div class="kpi kpi-warning">
                        <i class="icon-clock-o"></i>
                        <div class="kpi-content">
                            <span class="kpi-value">
                                {if $stats.cache_last_updated}
                                    {$stats.cache_last_updated|date_format:"%d/%m/%Y"}
                                {else}
                                    {l s='Never' mod='stequivalentproducttype'}
                                {/if}
                            </span>
                            <span class="kpi-title">{l s='Last Cache Update' mod='stequivalentproducttype'}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6">
        <div class="panel">
            <div class="panel-heading">
                <i class="icon-list"></i>
                {l s='Popular Equivalent Groups' mod='stequivalentproducttype'}
            </div>
            <div class="panel-body">
                {if $stats.popular_groups && count($stats.popular_groups) > 0}
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>{l s='Group Hash' mod='stequivalentproducttype'}</th>
                                <th class="text-center">{l s='Product Count' mod='stequivalentproducttype'}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {foreach from=$stats.popular_groups item=group}
                                <tr>
                                    <td><code>{$group.group_hash|truncate:20:"...":true}</code></td>
                                    <td class="text-center">
                                        <span class="badge badge-info">{$group.product_count|intval}</span>
                                    </td>
                                </tr>
                            {/foreach}
                        </tbody>
                    </table>
                {else}
                    <div class="alert alert-info">
                        <i class="icon-info-circle"></i>
                        {l s='No equivalent groups found yet.' mod='stequivalentproducttype'}
                    </div>
                {/if}
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="panel">
            <div class="panel-heading">
                <i class="icon-cogs"></i>
                {l s='Attribute Configuration' mod='stequivalentproducttype'}
            </div>
            <div class="panel-body">
                {if $stats.attribute_usage && count($stats.attribute_usage) > 0}
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>{l s='Attribute Name' mod='stequivalentproducttype'}</th>
                                <th class="text-center">{l s='Weight' mod='stequivalentproducttype'}</th>
                                <th class="text-center">{l s='Status' mod='stequivalentproducttype'}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {foreach from=$stats.attribute_usage item=attr}
                                <tr>
                                    <td><strong>{$attr.attribute_name|escape:'html':'UTF-8'}</strong></td>
                                    <td class="text-center">
                                        <span class="badge badge-primary">{$attr.weight|intval}</span>
                                    </td>
                                    <td class="text-center">
                                        {if $attr.is_enabled}
                                            <span class="badge badge-success">
                                                <i class="icon-check"></i> {l s='Enabled' mod='stequivalentproducttype'}
                                            </span>
                                        {else}
                                            <span class="badge badge-danger">
                                                <i class="icon-times"></i> {l s='Disabled' mod='stequivalentproducttype'}
                                            </span>
                                        {/if}
                                    </td>
                                </tr>
                            {/foreach}
                        </tbody>
                    </table>
                {else}
                    <div class="alert alert-warning">
                        <i class="icon-warning"></i>
                        {l s='No attribute mappings configured yet.' mod='stequivalentproducttype'}
                        <br>
                        <a href="{$link->getAdminLink('AdminStEquivalentProductType')}&add{$table}" class="btn btn-primary btn-sm">
                            <i class="icon-plus"></i> {l s='Add Attribute Mapping' mod='stequivalentproducttype'}
                        </a>
                    </div>
                {/if}
            </div>
        </div>
    </div>
</div>

<div class="panel">
    <div class="panel-heading">
        <i class="icon-info-circle"></i>
        {l s='Module Information' mod='stequivalentproducttype'}
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-lg-12">
                <h4>{l s='How it works:' mod='stequivalentproducttype'}</h4>
                <ul>
                    <li>{l s='The module automatically detects products with similar specifications based on configured attributes.' mod='stequivalentproducttype'}</li>
                    <li>{l s='Products are grouped by matching attribute values (color, size, reference, etc.).' mod='stequivalentproducttype'}</li>
                    <li>{l s='Sales data is cached for performance and updated hourly.' mod='stequivalentproducttype'}</li>
                    <li>{l s='The display can be customized through the main module configuration.' mod='stequivalentproducttype'}</li>
                </ul>
                
                <h4>{l s='Performance Tips:' mod='stequivalentproducttype'}</h4>
                <ul>
                    <li>{l s='Use attribute weights to prioritize important matching criteria.' mod='stequivalentproducttype'}</li>
                    <li>{l s='Disable unused attributes to improve matching performance.' mod='stequivalentproducttype'}</li>
                    <li>{l s='The sales cache is automatically maintained but can be cleared if needed.' mod='stequivalentproducttype'}</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.kpi-container {
    margin-bottom: 20px;
}

.kpi {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.kpi.kpi-primary {
    border-left: 4px solid #007cbb;
}

.kpi.kpi-success {
    border-left: 4px solid #5cb85c;
}

.kpi.kpi-info {
    border-left: 4px solid #5bc0de;
}

.kpi.kpi-warning {
    border-left: 4px solid #f0ad4e;
}

.kpi i {
    font-size: 24px;
    color: #999;
    margin-bottom: 10px;
    display: block;
}

.kpi-value {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.kpi-title {
    display: block;
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}
</style>
