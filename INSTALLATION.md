# Installation Guide - Equivalent Product Types Module

## 📋 System Requirements

### Minimum Requirements
- **PrestaShop**: 9.0 or higher
- **PHP**: 8.1 or higher
- **MySQL**: 5.7 or higher
- **Memory**: 128MB minimum (256MB recommended)
- **Disk Space**: 5MB for module files

### Recommended Environment
- **PHP**: 8.2+
- **MySQL**: 8.0+
- **Memory**: 512MB+
- **Web Server**: Apache 2.4+ or Nginx 1.18+

## 🚀 Installation Methods

### Method 1: Upload via PrestaShop Admin (Recommended)

1. **Prepare the Module**
   - Download the module as a ZIP file
   - Ensure the ZIP contains the `stequivalentproducttype` folder

2. **Run Setup Script (Optional but Recommended)**
   - Extract the module to your modules directory
   - Navigate to the module folder
   - Run: `php setup.php` to check dependencies and permissions
   - This will handle Composer dependencies automatically if available

3. **Upload via Admin Panel**
   - Log into your PrestaShop admin panel
   - Navigate to **Modules → Module Manager**
   - Click **"Upload a module"**
   - Select the ZIP file and upload
   - Wait for the upload to complete

4. **Install the Module**
   - Find "Equivalent Product Types" in the module list
   - Click **"Install"**
   - Wait for installation to complete

### Method 2: Manual FTP Upload

1. **Extract Module Files**
   ```bash
   unzip stequivalentproducttype.zip
   ```

2. **Upload via FTP**
   - Connect to your server via FTP
   - Navigate to `/modules/` directory
   - Upload the entire `stequivalentproducttype` folder
   - Ensure all files are uploaded correctly

3. **Set Permissions**
   ```bash
   chmod -R 755 modules/stequivalentproducttype/
   chmod -R 777 modules/stequivalentproducttype/translations/
   ```

4. **Install via Admin**
   - Go to **Modules → Module Manager**
   - Find "Equivalent Product Types"
   - Click **"Install"**

### Method 3: Command Line (Advanced)

1. **Clone Repository**
   ```bash
   cd /path/to/prestashop/modules/
   git clone https://github.com/your-repo/stequivalentproducttype.git
   ```

2. **Set Permissions**
   ```bash
   chmod -R 755 stequivalentproducttype/
   chmod -R 777 stequivalentproducttype/translations/
   ```

3. **Install Dependencies** (if using Composer)
   ```bash
   cd stequivalentproducttype/
   composer install --no-dev
   ```

## ⚙️ Post-Installation Configuration

### Step 1: Basic Configuration

1. **Access Module Configuration**
   - Go to **Modules → Module Manager**
   - Find "Equivalent Product Types"
   - Click **"Configure"**

2. **Enable the Module**
   - Set "Enable Module" to **"Yes"**
   - Click **"Save"**

3. **Configure Display Options**
   ```
   ✅ Show Product Images: Yes
   ✅ Show Prices: Yes
   ✅ Show Stock Information: Yes
   ✅ Show Sales Information: Yes
   📊 Display Mode: Grid (recommended)
   📅 Sales Period: 30 days
   ```

### Step 2: Attribute Configuration

1. **Access Attribute Mapping**
   - In module configuration, go to **"Attribute Mapping"** tab
   - Or navigate to **Modules → Equivalent Product Types → Attribute Mapping**

2. **Configure Default Attributes**
   The module comes with pre-configured attributes:
   - **Color** (Weight: 10, Enabled: Yes)
   - **Size** (Weight: 8, Enabled: Yes)
   - **Material** (Weight: 6, Enabled: Yes)

3. **Add Custom Attributes** (Optional)
   - Click **"Add Attribute Mapping"**
   - Select attribute group from dropdown
   - Set appropriate weight (1-10)
   - Enable the attribute
   - Save configuration

### Step 3: Test Installation

1. **Verify Database Tables**
   Check that these tables were created:
   ```sql
   SELECT TABLE_NAME FROM information_schema.TABLES 
   WHERE TABLE_SCHEMA = 'your_database' 
   AND TABLE_NAME LIKE 'ps_stequivalent%';
   ```

2. **Check Front Office Display**
   - Visit any product page with variants
   - Look for "Similar Products" section
   - Verify products are displaying correctly

3. **Test Admin Interface**
   - Access **Modules → Equivalent Product Types**
   - Check statistics dashboard
   - Verify configuration options work

## 🔧 Configuration Examples

### Example 1: Fashion Store Setup
```
Attributes to Match:
✅ Color (Weight: 10)
✅ Size (Weight: 9)
✅ Material (Weight: 7)
❌ Reference (Weight: 5)

Display Settings:
📱 Display Mode: Grid
🖼️ Show Images: Yes
💰 Show Prices: Yes
📦 Show Stock: Yes
📊 Show Sales: Yes (30 days)
```

### Example 2: Electronics Store Setup
```
Attributes to Match:
✅ Color (Weight: 8)
❌ Size (Weight: 5)
✅ Reference (Weight: 10)
✅ Brand (Weight: 9)

Display Settings:
📋 Display Mode: Table
🖼️ Show Images: Yes
💰 Show Prices: Yes
📦 Show Stock: Yes
📊 Show Sales: Yes (90 days)
```

### Example 3: Minimal Setup
```
Attributes to Match:
✅ Color (Weight: 10)
✅ Size (Weight: 8)
❌ Material (Weight: 0)
❌ Reference (Weight: 0)

Display Settings:
📱 Display Mode: List
🖼️ Show Images: No
💰 Show Prices: Yes
📦 Show Stock: Yes
📊 Show Sales: No
```

## 🔍 Verification Checklist

After installation, verify these items:

### ✅ Database Verification
- [ ] All 5 module tables created successfully
- [ ] Default attribute mappings inserted
- [ ] No SQL errors in PrestaShop logs

### ✅ File Verification
- [ ] All module files uploaded correctly
- [ ] Proper file permissions set
- [ ] CSS and JS files accessible

### ✅ Functionality Verification
- [ ] Module appears in admin module list
- [ ] Configuration page loads without errors
- [ ] Front office displays equivalent products
- [ ] No PHP errors in error logs

### ✅ Performance Verification
- [ ] Page load times acceptable
- [ ] No memory limit errors
- [ ] Cache system working properly

## 🚨 Troubleshooting Installation Issues

### Issue: Module Not Installing

**Symptoms:** Installation fails or module doesn't appear
**Solutions:**
1. Check file permissions (755 for folders, 644 for files)
2. Verify PHP memory limit (increase to 256MB+)
3. Check PrestaShop error logs
4. Ensure PrestaShop version compatibility

### Issue: Database Tables Not Created

**Symptoms:** Module installs but doesn't work
**Solutions:**
1. Check MySQL user permissions
2. Verify database connection
3. Manually run SQL from `sql/install.php`
4. Check for SQL syntax errors

### Issue: Front Office Not Displaying

**Symptoms:** No equivalent products shown on product pages
**Solutions:**
1. Clear PrestaShop cache
2. Check if products have required attributes
3. Verify module is enabled in configuration
4. Check template compatibility

### Issue: Autoloader Not Found Error

**Symptoms:** Error message "vendor/autoload.php file not defined" or similar
**Solutions:**
1. **Run the setup script:**
   ```bash
   cd modules/stequivalentproducttype/
   php setup.php
   ```

2. **Manual Composer installation:**
   ```bash
   cd modules/stequivalentproducttype/
   composer install --no-dev --optimize-autoloader
   ```

3. **If Composer is not available:**
   - The module will automatically use manual autoloading
   - No action required - this is normal and expected
   - Performance may be slightly reduced but functionality remains the same

4. **Alternative: Remove Composer dependency entirely:**
   ```bash
   # The module works without Composer
   # Manual autoloading is built-in as fallback
   ```

### Issue: Permission Errors

**Symptoms:** File access errors or upload failures
**Solutions:**
```bash
# Set correct permissions
chmod -R 755 modules/stequivalentproducttype/
chmod -R 777 modules/stequivalentproducttype/translations/
chown -R www-data:www-data modules/stequivalentproducttype/
```

## 📞 Getting Help

If you encounter issues during installation:

1. **Check System Requirements** - Ensure your environment meets all requirements
2. **Review Error Logs** - Check PrestaShop and server error logs
3. **Verify File Integrity** - Ensure all files uploaded correctly
4. **Test Step by Step** - Follow this guide exactly
5. **Contact Support** - Reach out with specific error messages

### Support Information
- **Email:** <EMAIL>
- **Documentation:** See README.md
- **Issue Tracker:** GitHub Issues (if applicable)

## 🔄 Upgrade Instructions

When upgrading from a previous version:

1. **Backup Current Installation**
   ```bash
   cp -r modules/stequivalentproducttype modules/stequivalentproducttype_backup
   ```

2. **Export Configuration**
   - Note current settings in admin panel
   - Export attribute mappings if customized

3. **Upload New Version**
   - Follow installation method above
   - Overwrite existing files

4. **Run Upgrade Process**
   - Module will automatically detect upgrade
   - Database schema will be updated if needed

5. **Verify Configuration**
   - Check all settings are preserved
   - Test functionality thoroughly

---

**Installation Complete!** 🎉

Your Equivalent Product Types module is now ready to use. Visit any product page to see it in action!
