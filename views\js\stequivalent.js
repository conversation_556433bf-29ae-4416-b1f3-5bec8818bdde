/**
 * JavaScript for Equivalent Product Types Module
 *
 * <AUTHOR>
 * @version 1.0.0
 */

$(document).ready(function() {
    // Initialize equivalent products functionality
    StEquivalentProducts.init();
});

var StEquivalentProducts = {
    
    /**
     * Initialize the module
     */
    init: function() {
        this.bindEvents();
        this.initTooltips();
        this.initLazyLoading();
        this.initFilterToggle();
    },
    
    /**
     * Bind event handlers
     */
    bindEvents: function() {
        // Handle product image hover effects
        $(document).on('mouseenter', '.stequivalent-product-image', function() {
            $(this).addClass('hover-effect');
        });
        
        $(document).on('mouseleave', '.stequivalent-product-image', function() {
            $(this).removeClass('hover-effect');
        });
        
        // Handle product item click (for grid/list items)
        $(document).on('click', '.stequivalent-product-item', function(e) {
            // Don't trigger if clicking on a link or button
            if ($(e.target).is('a, button') || $(e.target).closest('a, button').length > 0) {
                return;
            }
            
            var productLink = $(this).find('.stequivalent-product-link').first();
            if (productLink.length > 0) {
                window.location.href = productLink.attr('href');
            }
        });
        
        // Handle view button clicks with analytics
        $(document).on('click', '.stequivalent-view-btn', function(e) {
            var productName = $(this).closest('.stequivalent-product-item, tr').find('.stequivalent-product-link').text().trim();
            
            // Track click event if Google Analytics is available
            if (typeof gtag !== 'undefined') {
                gtag('event', 'click', {
                    'event_category': 'Equivalent Products',
                    'event_label': productName,
                    'value': 1
                });
            }
        });
        
        // Handle stock status updates
        this.updateStockStatus();
        
        // Handle responsive table scrolling
        this.initResponsiveTable();
    },
    
    /**
     * Initialize tooltips for additional information
     */
    initTooltips: function() {
        // Add tooltips to stock status
        $('.stequivalent-stock, .stock-status, .stock-badge').each(function() {
            var $this = $(this);
            var stockText = $this.text().trim();
            
            if (stockText.includes('In Stock')) {
                $this.attr('title', 'This product is currently available for purchase');
            } else if (stockText.includes('Out of Stock')) {
                $this.attr('title', 'This product is currently unavailable');
            }
        });
        
        // Add tooltips to sales information
        $('.stequivalent-sales, .stequivalent-item-sales').each(function() {
            var $this = $(this);
            $this.attr('title', 'Number of units sold in the specified period');
        });
        
        // Add tooltips to attribute badges
        $('.stequivalent-attribute-badge').each(function() {
            var $this = $(this);
            $this.attr('title', 'Product specification: ' + $this.text());
        });
        
        // Initialize Bootstrap tooltips if available
        if (typeof $.fn.tooltip !== 'undefined') {
            $('[title]').tooltip({
                placement: 'top',
                trigger: 'hover'
            });
        }
    },
    
    /**
     * Initialize lazy loading for product images
     */
    initLazyLoading: function() {
        // Simple lazy loading implementation
        var images = $('.stequivalent-product-image[data-src]');
        
        if (images.length > 0) {
            var imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        var img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            images.each(function() {
                imageObserver.observe(this);
            });
        }
    },
    
    /**
     * Initialize filter toggle functionality
     */
    initFilterToggle: function() {
        // Add filter controls if there are many products
        var productCount = $('.stequivalent-product-item').length;
        
        if (productCount > 6) {
            this.addFilterControls();
        }
    },
    
    /**
     * Add filter controls for large product lists
     */
    addFilterControls: function() {
        var $container = $('.stequivalent-block');
        var $filterContainer = $('<div class="stequivalent-filters"></div>');
        
        // Add stock filter
        var $stockFilter = $('<div class="stequivalent-filter-group">' +
            '<label>Show: </label>' +
            '<button type="button" class="btn btn-sm btn-default filter-btn active" data-filter="all">All</button>' +
            '<button type="button" class="btn btn-sm btn-success filter-btn" data-filter="in-stock">In Stock</button>' +
            '<button type="button" class="btn btn-sm btn-warning filter-btn" data-filter="out-of-stock">Out of Stock</button>' +
            '</div>');
        
        $filterContainer.append($stockFilter);
        $container.find('.stequivalent-description').after($filterContainer);
        
        // Bind filter events
        $(document).on('click', '.filter-btn', function() {
            var $btn = $(this);
            var filter = $btn.data('filter');
            
            // Update active state
            $btn.siblings().removeClass('active');
            $btn.addClass('active');
            
            // Apply filter
            StEquivalentProducts.applyFilter(filter);
        });
    },
    
    /**
     * Apply filter to product list
     */
    applyFilter: function(filter) {
        var $products = $('.stequivalent-product-item, .stequivalent-product-row');
        
        $products.show();
        
        if (filter === 'in-stock') {
            $products.each(function() {
                var $product = $(this);
                var hasInStock = $product.find('.in-stock').length > 0;
                if (!hasInStock) {
                    $product.hide();
                }
            });
        } else if (filter === 'out-of-stock') {
            $products.each(function() {
                var $product = $(this);
                var hasOutOfStock = $product.find('.out-of-stock').length > 0;
                if (!hasOutOfStock) {
                    $product.hide();
                }
            });
        }
        
        // Update visible count
        var visibleCount = $products.filter(':visible').length;
        $('.stequivalent-count').text('(' + visibleCount + ')');
    },
    
    /**
     * Update stock status dynamically
     */
    updateStockStatus: function() {
        // This could be enhanced to periodically check stock levels via AJAX
        $('.stequivalent-stock, .stock-status, .stock-badge').each(function() {
            var $this = $(this);
            var stockText = $this.text().toLowerCase();
            
            if (stockText.includes('out of stock') || stockText.includes('unavailable')) {
                $this.closest('.stequivalent-product-item, tr').addClass('out-of-stock-item');
            }
        });
    },
    
    /**
     * Initialize responsive table functionality
     */
    initResponsiveTable: function() {
        var $tableContainer = $('.stequivalent-table-container');
        
        if ($tableContainer.length > 0) {
            // Add scroll indicators
            $tableContainer.on('scroll', function() {
                var scrollLeft = $(this).scrollLeft();
                var scrollWidth = this.scrollWidth;
                var clientWidth = this.clientWidth;
                
                if (scrollLeft > 0) {
                    $(this).addClass('scrolled-left');
                } else {
                    $(this).removeClass('scrolled-left');
                }
                
                if (scrollLeft < scrollWidth - clientWidth) {
                    $(this).addClass('can-scroll-right');
                } else {
                    $(this).removeClass('can-scroll-right');
                }
            });
            
            // Trigger initial scroll check
            $tableContainer.trigger('scroll');
        }
    },
    
    /**
     * Animate product items on load
     */
    animateItems: function() {
        $('.stequivalent-product-item').each(function(index) {
            var $item = $(this);
            setTimeout(function() {
                $item.addClass('animate-in');
            }, index * 100);
        });
    },
    
    /**
     * Handle AJAX loading of additional products
     */
    loadMoreProducts: function(page) {
        // This could be implemented for pagination
        var currentProductId = $('#product').data('product-id') || window.id_product;
        
        if (!currentProductId) {
            return;
        }
        
        $.ajax({
            url: baseUri + 'modules/stequivalentproducttype/ajax.php',
            type: 'POST',
            data: {
                action: 'loadMore',
                id_product: currentProductId,
                page: page || 1
            },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.html) {
                    $('.stequivalent-products-grid, .stequivalent-list-container').append(response.html);
                    StEquivalentProducts.initTooltips();
                }
            },
            error: function() {
                console.log('Error loading more equivalent products');
            }
        });
    }
};

// Add CSS animations
var animationCSS = `
<style>
.stequivalent-product-item {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.stequivalent-product-item.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.stequivalent-product-image.hover-effect {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.stequivalent-table-container.scrolled-left::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 10px;
    background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
    pointer-events: none;
    z-index: 1;
}

.stequivalent-table-container.can-scroll-right::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 10px;
    background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
    pointer-events: none;
    z-index: 1;
}

.out-of-stock-item {
    opacity: 0.7;
}

.stequivalent-filters {
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

.stequivalent-filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.stequivalent-filter-group label {
    margin: 0;
    font-weight: bold;
}

.filter-btn {
    margin-right: 5px;
}

.filter-btn.active {
    background-color: #007cbb;
    border-color: #007cbb;
    color: white;
}
</style>`;

$('head').append(animationCSS);
